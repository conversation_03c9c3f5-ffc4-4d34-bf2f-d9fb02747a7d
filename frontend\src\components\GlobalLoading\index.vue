<template>
  <div class="global-loading">
    <v-overlay
      v-model="isVisible"
      class="align-center justify-center"
      :opacity="opacity"
      :z-index="zIndex"
      :scrim="scrimColor"
      :persistent="true"
    > 
      <div class="loading-container">
        <!-- 自定义加载动画 -->
        <div v-if="customTemplate" class="custom-loader">
          <slot></slot>
        </div>
        
        <!-- 默认加载动画 -->
        <div v-else class="default-loader">
          <v-progress-circular
            v-if="type === 'circular'"
            :size="size"
            :width="width"
            :color="color"
            indeterminate
          ></v-progress-circular>
          
          <v-progress-linear
            v-else-if="type === 'linear'"
            :height="height"
            :color="color"
            indeterminate
          ></v-progress-linear>
          
          <div class="loading-spinner" v-else>
            <div class="spinner">
              <div></div>
              <div></div>
            </div>
          </div>
        </div>
        
        <!-- 加载文本 -->
        <div v-if="text" class="loading-text" :style="{ color: textColor }">
          {{ text }}
        </div>
      </div>
    </v-overlay>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { ipc } from '@/utils/ipcRenderer';

// 加载状态
const isVisible = ref(false);
const text = ref('');
const type = ref('default'); // 'default', 'circular', 'linear'
const color = ref('primary');
const textColor = ref('#FFFFFF');
const size = ref(50);
const width = ref(4);
const height = ref(4);
const opacity = ref(0.8);
const zIndex = ref(9999);
const scrimColor = ref('rgba(0, 0, 0, 0.5)');
const customTemplate = ref(false);

// 计时器
let loadingTimer = null;
let autoHideTimer = null;

// 显示加载
const showLoading = (config = {}) => {
  // 清除之前的计时器
  clearTimers();
  
  // 设置配置
  text.value = config.text || '';
  type.value = config.type || 'default';
  color.value = config.color || 'primary';
  textColor.value = config.textColor || '#FFFFFF';
  size.value = config.size || 50;
  width.value = config.width || 4;
  height.value = config.height || 4;
  opacity.value = config.opacity !== undefined ? config.opacity : 0.8;
  zIndex.value = config.zIndex || 9999;
  scrimColor.value = config.scrimColor || 'rgba(0, 0, 0, 0.5)';
  customTemplate.value = config.customTemplate || false;
  
  // 显示加载
  isVisible.value = true;
  
  // 如果设置了自动隐藏
  if (config.autoHide && config.duration) {
    autoHideTimer = setTimeout(() => {
      hideLoading();
    }, config.duration);
  }
  
  return {
    hide: hideLoading,
    updateText: (newText) => {
      text.value = newText;
    }
  };
};

// 隐藏加载
const hideLoading = () => {
  isVisible.value = false;
  clearTimers();
};

// 更新加载文本
const updateLoadingText = (newText) => {
  text.value = newText;
};

// 清除计时器
const clearTimers = () => {
  if (loadingTimer) {
    clearTimeout(loadingTimer);
    loadingTimer = null;
  }
  
  if (autoHideTimer) {
    clearTimeout(autoHideTimer);
    autoHideTimer = null;
  }
};

// 处理IPC消息
const handleIpcMessage = (event, config) => {
  if (config.action === 'show') {
    showLoading(config);
  } else if (config.action === 'hide') {
    hideLoading();
  } else if (config.action === 'updateText') {
    updateLoadingText(config.text);
  }
};

onMounted(() => {
  // 监听IPC消息
  ipc.on('show-loading', handleIpcMessage);
});

onUnmounted(() => {
  // 移除IPC监听器
  ipc.removeListener('show-loading', handleIpcMessage);
  
  // 清除计时器
  clearTimers();
});

// 暴露方法给其他组件使用
defineExpose({
  showLoading,
  hideLoading,
  updateLoadingText
});
</script>

<style scoped lang="scss">
.global-loading {
  position: fixed;
  z-index: 9999;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-text {
  margin-top: 16px;
  font-size: 16px;
  text-align: center;
}

.default-loader {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner {
  position: relative;
  width: 60px;
  height: 60px;
}

.spinner > div {
  position: absolute;
  top: 50%;
  left: 50%;
  background: transparent;
  border-style: solid;
  border-width: 2px;
  border-radius: 100%;
  border-color: var(--v-theme-primary);
  animation: spinner-rotate 1s ease-in-out infinite;
}

.spinner > div:first-child {
  width: 60px;
  height: 60px;
  border-right-color: transparent;
  border-left-color: transparent;
}

.spinner > div:last-child {
  width: 30px;
  height: 30px;
  border-top-color: transparent;
  border-bottom-color: transparent;
  animation-duration: 0.5s;
  animation-direction: reverse;
}

@keyframes spinner-rotate {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  50% {
    transform: translate(-50%, -50%) rotate(180deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
</style>
