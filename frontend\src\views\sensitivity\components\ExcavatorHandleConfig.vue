<template>
  <div class="excavator-handle-config">
    <!-- 上部分：左右两组控制项 -->
    <div class="control-groups-container d-flex flex-row justify-between">
      <!-- 左侧控制组 -->
      <div class="control-group w-[380px]">
        <!-- 小臂升 -->
        <div class="control-row control-single-item">
          <control-item
            :key="'arm_up'"
            :item-key="'arm_up'"
            :label="'小臂升'"
            :is-selected="curSelectedKey === 'arm_up'"
            :form-data="handleForm.arm_up"
            :is-checked="localCheckList.includes('arm_up')"
            class="mx-auto"
            @select="onSelectItem"
            @update:checked="updateCheckList"
          />
        </div>
        
        <!-- 左回转/右回转 -->
        <div class="control-row control-double-items my-2 d-flex flex-row justify-space-between">
          <control-item
            :key="'swing_left'"
            :item-key="'swing_left'"
            :label="'左回转'"
            :is-selected="curSelectedKey === 'swing_left'"
            :form-data="handleForm.swing_left"
            :is-checked="localCheckList.includes('swing_left')"
            @select="onSelectItem"
            @update:checked="updateCheckList"
          />
          <control-item
            :key="'swing_right'"
            :item-key="'swing_right'"
            :label="'右回转'"
            :is-selected="curSelectedKey === 'swing_right'"
            :form-data="handleForm.swing_right"
            :is-checked="localCheckList.includes('swing_right')"
            @select="onSelectItem"
            @update:checked="updateCheckList"
          />
        </div>
        
        <!-- 小臂降 -->
        <div class="control-row control-single-item">
          <control-item
            :key="'arm_down'"
            :item-key="'arm_down'"
            :label="'小臂降'"
            :is-selected="curSelectedKey === 'arm_down'"
            :form-data="handleForm.arm_down"
            :is-checked="localCheckList.includes('arm_down')"
            class="mx-auto"
            @select="onSelectItem"
            @update:checked="updateCheckList"
          />
        </div>
      </div>

      <!-- 右侧控制组 -->
      <div class="control-group w-[380px]">
        <!-- 大臂降 -->
        <div class="control-row control-single-item">
          <control-item
            :key="'boom_down'"
            :item-key="'boom_down'"
            :label="'大臂降'"
            :is-selected="curSelectedKey === 'boom_down'"
            :form-data="handleForm.boom_down"
            :is-checked="localCheckList.includes('boom_down')"
            class="mx-auto"
            @select="onSelectItem"
            @update:checked="updateCheckList"
          />
        </div>
        
        <!-- 铲斗收/铲斗开 -->
        <div class="control-row control-double-items my-2 d-flex flex-row justify-space-between">
          <control-item
            :key="'bucket_down'"
            :item-key="'bucket_down'"
            :label="'铲斗收'"
            :is-selected="curSelectedKey === 'bucket_down'"
            :form-data="handleForm.bucket_down"
            :is-checked="localCheckList.includes('bucket_down')"
            @select="onSelectItem"
            @update:checked="updateCheckList"
          />
          <control-item
            :key="'bucket_up'"
            :item-key="'bucket_up'"
            :label="'铲斗开'"
            :is-selected="curSelectedKey === 'bucket_up'"
            :form-data="handleForm.bucket_up"
            :is-checked="localCheckList.includes('bucket_up')"
            @select="onSelectItem"
            @update:checked="updateCheckList"
          />
        </div>
        
        <!-- 大臂升 -->
        <div class="control-row control-single-item">
          <control-item
            :key="'boom_up'"
            :item-key="'boom_up'"
            :label="'大臂升'"
            :is-selected="curSelectedKey === 'boom_up'"
            :form-data="handleForm.boom_up"
            :is-checked="localCheckList.includes('boom_up')"
            class="mx-auto"
            @select="onSelectItem"
            @update:checked="updateCheckList"
          />
        </div>
      </div>
    </div>
    
    <!-- 下部分：履带控制项 -->
    <div class="track-controls-container mt-4 my-2 d-flex flex-row justify-space-between">
      <control-item
        :key="'track_left_front'"
        :item-key="'track_left_front'"
        :label="'左履前'"
        :is-selected="curSelectedKey === 'track_left_front'"
        :form-data="handleForm.track_left_front"
        :is-checked="localCheckList.includes('track_left_front')"
        @select="onSelectItem"
        @update:checked="updateCheckList"
      />
      <control-item
        :key="'track_left_after'"
        :item-key="'track_left_after'"
        :label="'左履后'"
        :is-selected="curSelectedKey === 'track_left_after'"
        :form-data="handleForm.track_left_after"
        :is-checked="localCheckList.includes('track_left_after')"
        @select="onSelectItem"
        @update:checked="updateCheckList"
      />
      <control-item
        :key="'track_right_front'"
        :item-key="'track_right_front'"
        :label="'右履前'"
        :is-selected="curSelectedKey === 'track_right_front'"
        :form-data="handleForm.track_right_front"
        :is-checked="localCheckList.includes('track_right_front')"
        @select="onSelectItem"
        @update:checked="updateCheckList"
      />
      <control-item
        :key="'track_right_after'"
        :item-key="'track_right_after'"
        :label="'右履后'"
        :is-selected="curSelectedKey === 'track_right_after'"
        :form-data="handleForm.track_right_after"
        :is-checked="localCheckList.includes('track_right_after')"
        @select="onSelectItem"
        @update:checked="updateCheckList"
      />
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, computed } from 'vue'
import ControlItem from './ControlItem.vue'

// 定义组件属性
const props = defineProps({
  // 当前选中的控制项
  curSelectedKey: {
    type: String,
    default: ''
  },
  // 选中的控制项列表
  checkList: {
    type: Array,
    default: () => []
  },
  // 手柄配置表单数据
  handleForm: {
    type: Object,
    required: true
  },
  // 设备类型
  deviceType: {
    type: String,
    default: 'excavator' // 默认为挖掘机
  }
})

// 定义事件
const emit = defineEmits(['update:checkList', 'update:curSelectedKey', 'select-item'])

// 创建计算属性处理checkList的双向绑定
const localCheckList = computed({
  get: () => props.checkList,
  set: (value) => {
    emit('update:checkList', value)
  }
})

// 选择控制项
const onSelectItem = (key) => {
  emit('update:curSelectedKey', key)
  emit('select-item', key)
}

// 更新复选框状态
const updateCheckList = (key) => {
  console.log('updateCheckList', key)
  console.log('父组件localCheckList computed', localCheckList.value)
  const newList = [...localCheckList.value]
  const index = newList.indexOf(key)
  
  if (index === -1) {
    newList.push(key)
  } else {
    newList.splice(index, 1)
  }

  console.log('新的checkList', newList)
  
  emit('update:checkList', newList)
}
</script>

<style scoped>
/* 布局容器样式 */
.excavator-handle-config {
  width: 100%;
}

.control-groups-container {
  width: 100%;
}

.control-group {
  display: flex;
  flex-direction: column;
}

.control-row {
  width: 100%;
}

.control-single-item {
  display: flex;
  justify-content: center;
}

.control-double-items {
  display: flex;
  justify-content: space-between;
}

.track-controls-container {
  width: 100%;
}

/* 继承的样式 */
.item-selected {
  border: 2px solid var(--v-primary-base);
  background-color: rgba(var(--v-theme-primary), 0.3) !important;
}

/* .bg-primary-light-9 {
  background-color: rgba(var(--v-theme-primary), 0.1);
} */

.chart-wrap {
  margin-bottom: 20px;
}
</style>