<template>
  <div
    class="item-wrap p-2 pb-3 w-36 h-36 rounded d-flex flex-column bg-primary-light-9"
    :class="{ 'item-selected': isSelected }"
    @click="$emit('select', itemKey)"
  >
    <div class="item-name mx-auto">
      <div class="custom-checkbox d-flex align-center">
        <input
          type="checkbox"
          :id="`checkbox-${itemKey}`"
          :checked="isChecked"
          class="custom-checkbox-input"
          @change="toggleCheckbox"
        />
        <label :for="`checkbox-${itemKey}`" class="custom-checkbox-label ml-2">{{ label }}</label>
      </div>
    </div>
    <div class="item-params tracking-[1px] d-flex flex-column justify-space-around">
      <p>
        最小值:
        <span class="text-primary font-weight-bold">{{ formData.low_limit }}</span>
      </p>
      <p>
        最大值:
        <span class="text-primary font-weight-bold">{{ formData.up_limit }}</span>
      </p>
      <p>
        灵敏度:
        <span class="text-primary font-weight-bold">{{ formData.sensitivity }}</span>
      </p>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from "vue";

// 定义组件属性
const props = defineProps({
  // 控制项的键名
  itemKey: {
    type: String,
    required: true,
  },
  // 控制项的显示标签
  label: {
    type: String,
    required: true,
  },
  // 是否被选中（高亮显示）
  isSelected: {
    type: Boolean,
    default: false,
  },
  // 是否被勾选（复选框状态）
  isChecked: {
    type: Boolean,
    default: false,
  },
  // 表单数据（包含最小值、最大值、灵敏度）
  formData: {
    type: Object,
    required: true,
  },
});

// 定义事件
const emit = defineEmits(["select", "update:checked"]);

// 切换复选框状态
const toggleCheckbox = () => {
  console.log("toggleCheckbox", props.isChecked);
  emit("update:checked", props.itemKey);
};
</script>

<style scoped>
.item-wrap {
  cursor: pointer;
  user-select: none;
}

.item-wrap .item-name {
  flex: 1;
}

.item-wrap .item-params {
  flex: 3;
}

.item-selected {
  background-color: rgba(var(--v-theme-primary), 0.3) !important;
  border: 2px solid var(--v-primary-base);
}

.bg-primary-light-9 {
  background-color: #2c2c2c;
}

/* 自定义复选框样式 */
.custom-checkbox {
  position: relative;
  width: 100%;
  height: 40px;
}

.custom-checkbox-input {
  appearance: none;
  -webkit-appearance: none;
  width: 30px;
  height: 30px;
  border: 2px solid rgba(var(--v-theme-primary), 0.7);
  border-radius: 4px;
  background-color: transparent;
  cursor: pointer;
  position: relative;
  vertical-align: middle;
}

.custom-checkbox-input:checked {
  background-color: rgba(var(--v-theme-primary), 1);
}

.custom-checkbox-input:checked::after {
  content: "";
  position: absolute;
  top: 2px;
  left: 8px;
  width: 10px;
  height: 20px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.custom-checkbox-label {
  cursor: pointer;
  font-size: 16px;
  user-select: none;
}
</style>
