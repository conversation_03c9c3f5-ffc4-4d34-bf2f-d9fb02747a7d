# BUX-Electron 辅助屏幕应用测试计划

## 1. 测试目标

对BUX-Electron辅助屏幕应用进行全面测试，确保其功能正常、界面友好、性能稳定，满足用户需求。

## 2. 测试范围

根据对项目代码的分析，本测试计划主要针对以下功能模块：

- 欢迎界面（Hello页面）
- 语言选择功能
- 车辆选择功能
- 系统登录功能
- WebSocket通信功能
- 界面状态切换
- Debug模块功能
- 参数配置功能
- 日志查看功能
- 网络通信功能

## 3. 测试环境

- 操作系统：Windows
- 应用版本：当前开发版本
- 测试设备：操作台辅助平板

## 4. 测试用例

### 4.1 欢迎界面测试

#### TC-001: 欢迎界面显示测试

**测试步骤：**
1. 启动应用
2. 观察欢迎界面的显示情况

**预期结果：**
- 欢迎界面正确显示，包括星空背景动画效果
- 界面上显示"进入系统"按钮

#### TC-002: 语言选择功能测试

**测试步骤：**
1. 进入语言选择界面
2. 选择不同语言选项（中文、English、日本語）
3. 点击确认选择按钮

**预期结果：**
- 语言选项正确显示且可选择
- 选中状态正确显示
- 确认按钮点击后正确切换语言

#### TC-003: 车辆选择功能测试

**测试步骤：**
1. 进入车辆选择界面
2. 查看可选车辆列表
3. 选择不同车辆
4. 切换异步监测开关

**预期结果：**
- 车辆列表正确显示
- 车辆选择状态正确更新
- 异步监测开关状态正确切换

### 4.2 Debug模块测试

#### TC-004: 画面播放功能测试

**测试步骤：**
1. 进入Debug模块
2. 测试画面播放控制功能
3. 检查画面显示效果

**预期结果：**
- 画面正常播放

#### TC-005: MQTT调试功能测试

**测试步骤：**
1. 打开MQTT调试界面
2. 测试消息发送功能
3. 检查消息接收显示

**预期结果：**
- 消息正确发送
- 接收消息正确显示
- 消息格式符合预期

### 4.3 参数配置测试

#### TC-006: 参数设置功能测试

**测试步骤：**
1. 进入参数配置界面
2. 修改各项参数值
3. 保存配置
4. 重新加载检查配置

**预期结果：**
- 参数修改界面正常显示
- 参数值正确保存
- 重新加载后参数值保持一致

### 4.4 日志查看测试

#### TC-007: 日志功能测试

**测试步骤：**
1. 进入日志查看界面
2. 测试日志筛选功能
3. 测试日志导出功能

**预期结果：**
- 日志内容正确显示
- 筛选功能正常工作
- 日志导出功能正常

### 4.5 网络通信测试

#### TC-008: 网络通信测试

**测试步骤：**
1. 测试当前设备到目标设备的延迟
2. 目标设备有 服务器，安卓，ROS

**预期结果：**
- 消息正确发送和接收显示网络延迟

#### TC-009: MQTT通信测试

**测试步骤：**
1. 配置MQTT连接参数
2. 测试主题订阅功能
3. 测试消息发布功能

**预期结果：**
- MQTT连接正常建立
- 主题订阅成功
- 消息发布和接收正常

### 4.6 界面交互测试

#### TC-010: 按钮响应测试

**测试步骤：**
1. 测试各页面按钮点击
2. 检查按钮状态变化
3. 验证按钮功能执行

**预期结果：**
- 按钮点击响应正常
- 状态变化正确显示
- 功能正确执行

#### TC-011: 页面切换测试

**测试步骤：**
1. 测试不同页面间的切换
2. 检查页面切换动画
3. 验证页面状态保持

**预期结果：**
- 页面切换流畅
- 动画效果正常
- 页面状态正确保持