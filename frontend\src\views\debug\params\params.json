{"vehicle_id": "66f135aa8ea96a77201f53e9", "vehicle_name": "测试机器-wlf", "vehicle_type": 1, "vehicle_sub_type": 1, "online": false, "is_lock": 1, "locked_opc": "66f4c31a8c6a933e2924143d", "android": {"handlerType": 0, "projectName": "test_waji_big_01", "payEnccn": "F1244C3E79B73BC51E6A605C1E3DDE5A0D983648A94371BEE4BF85266F524E2F", "mqttParams": {"mqttClientID": "test_waji_big_01_main_operator_android_001_test", "mqttHost": "tcp://mqtt.apps.builderx.com:1883", "mqttKeepLive": "test_666/keeplive", "mqttName": "builderx", "mqttPassword": "builderx", "mqttToAndroid": "test_666/ros_to_android", "mqttToExcavator": "test_666/android_to_ros_test", "mqttToTripartite": "", "mqttToAndroidTripartite": "", "mqttToAndroidTripartiteEx001": ""}, "playParams": {"playUrlVideo": "rtsp://api.builderx.com/live/pro/test_666/stream0", "playUrlAudio": "rtsp://api.builderx.com/live/pro/test_666/voice", "playUrlShow": "", "videoType": 0}, "powerOnParams": {"niRenDTO": true, "powerOnCommand": "", "powerOffCommand": "", "allowPowerOff": true, "allowPowerOn": false, "mqttToAndroidPowerON": "/test_666/power_on_test", "androidToMqttPowerON": "/test_666/power_status", "powerQuery": false}, "selfTestParams": {"controlMecIp": "************"}, "platformParams": {"host": "", "port": ""}, "networkParams": {"delayMode": false, "pingPort": 12345, "ntpUrl": ""}, "switchExtendedFunctionParams": {"selfStart": false, "haveScreenRecording": false, "haveParrotAgent": false, "hadRadarHalt": false, "havePilotControl": false, "leftHandleReversal": false, "sendExtendParam": false, "usesTheLeftHandle": false, "simulatePedal": false, "mixedFlowSetting": true, "doubleDipAngle": true, "needSend303": true}}, "handleConfig": {"handleSettingList": [{"isPublic": false, "isUse": true, "step_switch": false, "arm_down": {"low_limit": 7, "sensitivity": 1, "step": 10, "up_limit": 100}, "arm_up": {"low_limit": 9, "sensitivity": 1, "step": 10, "up_limit": 100}, "boom_down": {"low_limit": 7, "sensitivity": 1, "step": 10, "up_limit": 100}, "boom_up": {"low_limit": 7, "sensitivity": 1, "step": 10, "up_limit": 100}, "bucket_down": {"low_limit": 7, "sensitivity": 1, "step": 10, "up_limit": 100}, "bucket_up": {"low_limit": 7, "sensitivity": 1, "step": 10, "up_limit": 100}, "simulate_left_after": {"low_limit": 7, "sensitivity": 1, "step": 10, "up_limit": 100}, "simulate_left_front": {"low_limit": 7, "sensitivity": 1, "step": 10, "up_limit": 100}, "simulate_right_after": {"low_limit": 7, "sensitivity": 1, "step": 10, "up_limit": 100}, "simulate_right_front": {"low_limit": 7, "sensitivity": 1, "step": 10, "up_limit": 100}, "swing_left": {"low_limit": 7, "sensitivity": 1, "step": 10, "up_limit": 100}, "swing_right": {"low_limit": 7, "sensitivity": 1, "step": 10, "up_limit": 100}, "track_left_after": {"low_limit": 7, "sensitivity": 1, "step": 10, "up_limit": 100}, "track_left_front": {"low_limit": 7, "sensitivity": 1, "step": 10, "up_limit": 100}, "track_right_after": {"low_limit": 7, "sensitivity": 1, "step": 10, "up_limit": 100}, "track_right_front": {"low_limit": 7, "sensitivity": 1, "step": 10, "up_limit": 100}}, {"isPublic": false, "isUse": false, "step_switch": false, "arm_down": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}, "arm_up": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}, "boom_down": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}, "boom_up": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}, "bucket_down": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}, "bucket_up": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}, "simulate_left_after": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}, "simulate_left_front": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}, "simulate_right_after": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}, "simulate_right_front": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}, "swing_left": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}, "swing_right": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}, "track_left_after": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}, "track_left_front": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}, "track_right_after": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}, "track_right_front": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}}, {"isPublic": false, "isUse": false, "step_switch": false, "arm_down": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}, "arm_up": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}, "boom_down": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}, "boom_up": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}, "bucket_down": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}, "bucket_up": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}, "simulate_left_after": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}, "simulate_left_front": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}, "simulate_right_after": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}, "simulate_right_front": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}, "swing_left": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}, "swing_right": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}, "track_left_after": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}, "track_left_front": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}, "track_right_after": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}, "track_right_front": {"low_limit": 0, "sensitivity": 0, "step": 10, "up_limit": 100}}]}}