<template>
  <div>
    <canvas id="c"></canvas>
  </div>
</template>
<script setup>
import { onMounted, onUnmounted, ref } from "vue";

let animationFrameId;
let PARTICLE_NUM = 500;
let PARTICLE_BASE_RADIUS = 0.5;
let FL = 500;
let DEFAULT_SPEED = 5;
let BOOST_SPEED = 200;
let canvas;
let canvasWidth, canvasHeight;
let context;
let centerX, centerY;
let mouseX, mouseY;
let speed = DEFAULT_SPEED;
let targetSpeed = DEFAULT_SPEED;
let particles = [];
const isActive = ref(false);

function loop() {
  context.save();
  context.fillStyle = "rgb(0, 0, 0)";
  context.fillRect(0, 0, canvasWidth, canvasHeight);
  context.restore();
  speed += (targetSpeed - speed) * 0.01;
  let n;
  let h, j;
  let v, w;
  let k, A, B, u;
  let o, s, t, q;
  let b, c, d;
  let l = Math.PI * 0.5;
  let e = Math.atan2;
  let g = Math.cos;
  let z = Math.sin;
  context.beginPath();
  for (let m = 0; m < PARTICLE_NUM; m++) {
    n = particles[m];
    n.pastZ = n.z;
    n.z -= speed;
    if (n.z <= 0) {
      randomizeParticle(n);
      continue;
    }
    h = centerX - (mouseX - centerX) * 1.25;
    j = centerY - (mouseY - centerY) * 1.25;
    v = n.x - h;
    w = n.y - j;
    k = FL / n.z;
    A = h + v * k;
    B = j + w * k;
    u = PARTICLE_BASE_RADIUS * k;
    o = FL / n.pastZ;
    s = h + v * o;
    t = j + w * o;
    q = PARTICLE_BASE_RADIUS * o;
    b = e(t - B, s - A);
    c = b + l;
    d = b - l;
    context.moveTo(s + q * g(c), t + q * z(c));
    context.arc(s, t, q, c, d, true);
    context.lineTo(A + u * g(d), B + u * z(d));
    context.arc(A, B, u, d, c, true);
    context.closePath();
  }
  context.fill();
  animationFrameId = requestAnimationFrame(loop);
}
function randomizeParticle(a) {
  a.x = Math.random() * canvasWidth;
  a.y = Math.random() * canvasHeight;
  a.z = Math.random() * 1500 + 500;
  return a;
}
function Particle(a, b, c) {
  this.x = a || 0;
  this.y = b || 0;
  this.z = c || 0;
  this.pastZ = 0;
}

onMounted(() => {
  canvas = document.getElementById("c");
  const c = () => {
    canvasWidth = canvas.width = window.innerWidth;
    canvasHeight = canvas.height = window.innerHeight;
    centerX = canvasWidth * 0.5;
    centerY = canvasHeight * 0.5;
    context = canvas.getContext("2d");
    context.fillStyle = "rgb(255, 255, 255)";
  };
  document.addEventListener("resize", c);
  c();
  mouseX = centerX;
  mouseY = centerY;
  for (let a = 0, b; a < PARTICLE_NUM; a++) {
    particles[a] = randomizeParticle(new Particle());
    particles[a].z -= 500 * Math.random();
  }
  requestAnimationFrame(loop);
});

let pressTimer = null;
const PRESS_DURATION = 1500; // 长按触发时间为1.5秒

const handleMouseStart = (e) => {
  targetSpeed = BOOST_SPEED;
  isActive.value = true;

  pressTimer = setTimeout(() => {
    router.push("/switch");
  }, PRESS_DURATION);
};

const handleMouseEnd = (e) => {
  targetSpeed = DEFAULT_SPEED;
  isActive.value = false;

  if (pressTimer) {
    clearTimeout(pressTimer);
    pressTimer = null;
  }
};

defineExpose({ handleMouseStart, handleMouseEnd });

</script>
<style scoped lang="scss">
canvas {
  height: 100vh;
  width: 100vw;
  background-color: hsl(256, 100%, 5%);
}
</style>
