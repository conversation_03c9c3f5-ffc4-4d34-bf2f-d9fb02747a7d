<template>
  <v-card class="pa-4">
    <v-card-title class="text-h5 mb-4">设备数据显示配置</v-card-title>
    <v-form>
      <!-- 基础显示设置 -->
      <v-row>
        <v-col cols="12">
          <v-card class="mb-4" variant="outlined">
            <v-card-title class="text-subtitle-1">基础显示设置</v-card-title>
            <v-card-text>
              <v-row>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>显示开机自检</span>
                    <v-switch
                      v-model="formData.showPowerOnSelfTest"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>显示手柄指示器</span>
                    <v-switch
                      v-model="formData.showRockerView"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>显示姿态仪</span>
                    <v-switch
                      v-model="formData.showBalancerView"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>远程上下电是否有效</span>
                    <v-switch
                      v-model="formData.showRemoteState"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>隐藏上电状态</span>
                    <v-switch
                      v-model="formData.hidePowerOnState"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>显示减油门脚踏板UI</span>
                    <v-switch
                      v-model="formData.showReduceThrottle"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>显示功率模式</span>
                    <v-switch
                      v-model="formData.showPowerMode"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>显示档位</span>
                    <v-switch
                      v-model="formData.showGear"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>显示一键复位</span>
                    <v-switch
                      v-model="formData.showAuto"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>

      <!-- 发动机数据显示 -->
      <v-row>
        <v-col cols="12">
          <v-card class="mb-4" variant="outlined">
            <v-card-title class="text-subtitle-1">发动机数据显示</v-card-title>
            <v-card-text>
              <v-row>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>显示发动机工作小时数</span>
                    <v-switch
                      v-model="formData.showEngineTime"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>显示发动机转速</span>
                    <v-switch
                      v-model="formData.showEngineSpeed"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>显示瞬时油耗</span>
                    <v-switch
                      v-model="formData.showOilConsumption"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>显示怠速</span>
                    <v-switch
                      v-model="formData.showIdle"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>

      <!-- 液压系统显示 -->
      <v-row>
        <v-col cols="12">
          <v-card class="mb-4" variant="outlined">
            <v-card-title class="text-subtitle-1">液压系统显示</v-card-title>
            <v-card-text>
              <v-row>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>显示左主泵压力</span>
                    <v-switch
                      v-model="formData.showLeftMainPumpPressure"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>显示右主泵压力</span>
                    <v-switch
                      v-model="formData.showRightMainPumpPressure"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>显示液压油温度</span>
                    <v-switch
                      v-model="formData.showHydraulicOilTemp"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>显示传动油温</span>
                    <v-switch
                      v-model="formData.showTransmissionOilTemp"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>

      <!-- 其他状态显示 -->
      <v-row>
        <v-col cols="12">
          <v-card class="mb-4" variant="outlined">
            <v-card-title class="text-subtitle-1">其他状态显示</v-card-title>
            <v-card-text>
              <v-row>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>显示燃油油位</span>
                    <v-switch
                      v-model="formData.showOilLevel"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>显示水温</span>
                    <v-switch
                      v-model="formData.showWaterTemp"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>显示电池电压</span>
                    <v-switch
                      v-model="formData.showBatteryVoltage"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>显示机油压力</span>
                    <v-switch
                      v-model="formData.showOilPressure"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>显示转向灯</span>
                    <v-switch
                      v-model="formData.showTurnSignal"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>显示环境温度</span>
                    <v-switch
                      v-model="formData.showEnvironmentTemp"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>显示尿素液位</span>
                    <v-switch
                      v-model="formData.showUreaLevel"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>显示行走速度</span>
                    <v-switch
                      v-model="formData.showTravelSpeed"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>

      <!-- 灯光支持 -->
      <v-row>
        <v-col cols="12">
          <v-card class="mb-4" variant="outlined">
            <v-card-title class="text-subtitle-1">灯光支持</v-card-title>
            <v-card-text>
              <v-row>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>支持双闪灯</span>
                    <v-switch
                      v-model="formData.doubleFlashTurnLamp"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>支持示宽灯</span>
                    <v-switch
                      v-model="formData.wideLight"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>支持近光灯</span>
                    <v-switch
                      v-model="formData.headlamp"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>支持远光灯</span>
                    <v-switch
                      v-model="formData.taillight"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>

      <!-- 时间显示 -->
      <v-row>
        <v-col cols="12">
          <v-card class="mb-4" variant="outlined">
            <v-card-title class="text-subtitle-1">时间显示</v-card-title>
            <v-card-text>
              <v-row>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>显示本次开机时长</span>
                    <v-switch
                      v-model="formData.thisBootTime"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>显示本次作业时长</span>
                    <v-switch
                      v-model="formData.thisOperateDuration"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>显示总开机时长</span>
                    <v-switch
                      v-model="formData.allBootTime"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>显示总作业时长</span>
                    <v-switch
                      v-model="formData.allOperateDuration"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>

      <!-- 阈值设置 -->
      <v-row>
        <v-col cols="12">
          <v-card class="mb-4" variant="outlined">
            <v-card-title class="text-subtitle-1">阈值设置</v-card-title>
            <v-card-text>
              <v-row>
                <v-col cols="12" md="6">
                  <Keyboard
                    v-model="formData.noIgnitionMinSpeedValue"
                    label="不发点火最低转速值"
                    :rules="numericRules"
                    outlined
                    dense
                  />
                </v-col>
                <v-col cols="12" md="6">
                  <Keyboard
                    v-model="formData.oilTemperatureThreshold"
                    label="油温阈值"
                    :rules="numericRules"
                    outlined
                    dense
                  />
                </v-col>
                <v-col cols="12" md="6">
                  <Keyboard
                    v-model="formData.waterTemperatureThreshold"
                    label="水温阈值"
                    :rules="numericRules"
                    outlined
                    dense
                  />
                </v-col>
                <v-col cols="12" md="6">
                  <Keyboard
                    v-model="formData.oilPressureThreshold"
                    label="机油压力阈值"
                    :rules="numericRules"
                    outlined
                    dense
                  />
                </v-col>
                <v-col cols="12" md="6">
                  <Keyboard
                    v-model="formData.voltageThreshold"
                    label="电池电压阈值"
                    :rules="numericRules"
                    outlined
                    dense
                  />
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-form>
  </v-card>
</template>

<script setup>
import { cloneDeep, isEqual } from "lodash-es";
import { ref, reactive, onMounted, watch } from "vue";
import Keyboard from "@/components/Keyboard/index.vue";
import { ipc } from "@/utils/ipcRenderer";
import { sendParams2Android } from "@/utils/androidMessage";
import { findDifferences } from "@/utils/diffUtils";

// 表单数据
const formData = reactive({
  // 显示开机自检
  showPowerOnSelfTest: true,

  // 是否展示手柄指示器
  showRockerView: true,

  // 是否展示姿态仪
  showBalancerView: true,

  // 远程上下电是否有效显示
  showRemoteState: false,

  // 上电状态是否显示，日本004有些没有远程上电，不显示上电中等
  hidePowerOnState: false,

  // 发动机工作小时数
  showEngineTime: false,

  // 左主泵压力
  showLeftMainPumpPressure: false,

  // 右主泵压力
  showRightMainPumpPressure: false,

  // 是否展示减油门脚踏板UI，推土机
  showReduceThrottle: false,

  // 是否展示功率模式 柳工项目使用
  showPowerMode: false,

  // 是否展示档位
  showGear: false,

  // 是否燃油油位
  showOilLevel: true,

  // 展示发动机转速
  showEngineSpeed: true,

  // 是否水温 (冷却液温度)
  showWaterTemp: true,

  // 是否电池电压
  showBatteryVoltage: true,

  // 是否展示机油压力
  showOilPressure: true,

  // 是否展示液压油温度
  showHydraulicOilTemp: true,

  // 是否展示展示转向灯
  showTurnSignal: true,

  // 是否展示一键复位
  showAuto: true,

  // 是否展示瞬时油耗
  showOilConsumption: true,

  // 是否展示环境温度
  showEnvironmentTemp: true,

  // 是否展示尿素液位
  showUreaLevel: true,

  // 是否展示传动油温
  showTransmissionOilTemp: true,

  // 是否展示怠速
  showIdle: true,

  // 是否展示行走速度
  showTravelSpeed: true,

  // 是否支持双闪灯
  doubleFlashTurnLamp: false,

  // 是否支持示宽灯
  wideLight: false,

  // 是否支持近光灯
  headlamp: false,

  // 是否支持远光灯
  taillight: false,

  // 是否展示本次开机时长
  thisBootTime: true,

  // 是否展示本次作业时长
  thisOperateDuration: true,

  // 是否展示总开机时长
  allBootTime: true,

  // 是否展示总作业时长
  allOperateDuration: true,

  // 水温阈值
  waterTemperatureThreshold: 95,

  // 油温阈值
  oilTemperatureThreshold: 90,

  // 机油压力阈值
  oilPressureThreshold: 2000,

  // 电池电压阈值
  voltageThreshold: 23,

  // 不发点火最低转速值
  noIgnitionMinSpeedValue: 600,
});

// 验证规则
const numericRules = [(v) => !!v || "数值不能为空", (v) => !isNaN(Number(v)) || "必须是数字"];

let prevSettings = null;

watch(
  () => formData,
  (newVal) => {
    if (prevSettings && !isEqual(prevSettings, newVal)) {
      let diffList = findDifferences(newVal, prevSettings);
      if (diffList.length > 0) {
        diffList.forEach((item) => {
          sendParams2Android("android.showViewParams." + item.path, item.newValue);
        });
      }
    }
    prevSettings = cloneDeep(newVal);
  },
  {
    deep: true,
  }
);

// 初始化
onMounted(async () => {
  try {
    const savedData = await ipc.invoke("global-state:get", "android.showViewParams");
    if (savedData && Object.keys(savedData).length > 0) {
      Object.assign(formData, savedData);
      console.log("设备数据显示配置已加载", savedData);
    }
  } catch (error) {
    console.error("获取设备数据显示配置失败:", error);
  }
});
</script>

<style scoped>
.setting-control {
  max-width: 60px;
}
</style>
