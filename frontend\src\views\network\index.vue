<template>
  <div class="d-flex">
    <!-- 左侧垂直选项卡 -->
    <div class="tabs-container">
      <v-tabs v-model="tab" color="primary" direction="vertical" class="network-tabs">
        <v-tab v-for="item in tabItems" :key="item.value" :value="item.value">
          {{ item.title }}
        </v-tab>
      </v-tabs>
    </div>

    <!-- 右侧功能区域 -->
    <div class="content-container">
      <v-window v-model="tab">
        <!-- 时延曲线图视图 -->
        <v-window-item value="delay-chart">
          <v-card class="content-card">
            <!-- 当前时延曲线图 -->
            <div class="section-container">
              <div class="section-header">
                <span class="section-title">当前时延曲线图</span>
                <div class="d-flex align-center">
                  <v-btn :color="settings.isShowCurrentDelay ? 'error' : 'success'" @click="toggleCurrentDelay">
                    {{ settings.isShowCurrentDelay ? "关闭" : "打开" }}
                  </v-btn>
                </div>
              </div>
              <div class="section-content">
                <div class="chart-options">
                  <div class="option-row">
                    <div class="option-item">
                      <div class="option-label">遥控端</div>
                      <v-checkbox
                        hide-details
                        density="compact"
                        color="primary"
                        class="option-control"
                        v-model="settings.currentDelay.remoteControl"
                        :disabled="!settings.isShowCurrentDelay"
                        @change="sendDelaySettings"
                      ></v-checkbox>
                    </div>
                    <div class="option-item">
                      <div class="option-label">设备端</div>
                      <v-checkbox
                        hide-details
                        density="compact"
                        color="primary"
                        class="option-control"
                        v-model="settings.currentDelay.deviceEnd"
                        :disabled="!settings.isShowCurrentDelay"
                        @change="sendDelaySettings"
                      ></v-checkbox>
                    </div>
                    <div class="option-item">
                      <div class="option-label">遥控端丢包率</div>
                      <v-checkbox
                        hide-details
                        density="compact"
                        color="primary"
                        class="option-control"
                        v-model="settings.currentDelay.remotePacketLoss"
                        :disabled="!settings.isShowCurrentDelay"
                        @change="sendDelaySettings"
                      ></v-checkbox>
                    </div>
                  </div>
                  <div class="option-row">
                    <div class="option-item">
                      <div class="option-label">设备端丢包率</div>
                      <v-checkbox
                        hide-details
                        density="compact"
                        color="primary"
                        class="option-control"
                        v-model="settings.currentDelay.devicePacketLoss"
                        :disabled="!settings.isShowCurrentDelay"
                        @change="sendDelaySettings"
                      ></v-checkbox>
                    </div>
                    <div class="option-item">
                      <div class="option-label">视频抖动值</div>
                      <v-checkbox
                        hide-details
                        density="compact"
                        color="primary"
                        class="option-control"
                        v-model="settings.currentDelay.videoJitter"
                        :disabled="!settings.isShowCurrentDelay"
                        @change="sendDelaySettings"
                      ></v-checkbox>
                    </div>
                    <div class="option-item">
                      <div class="option-label">指令抖动值</div>
                      <v-checkbox
                        hide-details
                        density="compact"
                        color="primary"
                        class="option-control"
                        v-model="settings.currentDelay.commandJitter"
                        :disabled="!settings.isShowCurrentDelay"
                        @change="sendDelaySettings"
                      ></v-checkbox>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 历史时延曲线图 -->
            <div class="section-container mt-4">
              <div class="section-header">
                <span class="section-title">历史时延曲线图</span>
                <div class="d-flex align-center">
                  <v-btn
                    :color="settings.listDisplay ? 'error' : 'success'"
                    :disabled="!settings.isShowHistoryDelay"
                    @click="listDisplay"
                  >
                    {{ settings.listDisplay ? "关闭列表展示" : "打开列表展示" }}
                  </v-btn>
                  <v-btn
                    class="ml-4"
                    :color="settings.isShowHistoryDelay ? 'error' : 'success'"
                    @click="toggleHistoryDelay"
                  >
                    {{ settings.isShowHistoryDelay ? "关闭" : "打开" }}
                  </v-btn>
                </div>
              </div>
              <div class="section-content">
                <div class="chart-options">
                  <div class="option-row">
                    <div class="option-item">
                      <div class="option-label">遥控端</div>
                      <v-checkbox
                        hide-details
                        density="compact"
                        color="primary"
                        class="option-control"
                        v-model="settings.historyDelay.remoteControl"
                        :disabled="!settings.isShowHistoryDelay"
                        @change="sendHistoryDelaySettings"
                      ></v-checkbox>
                    </div>
                    <div class="option-item">
                      <div class="option-label">设备端</div>
                      <v-checkbox
                        hide-details
                        density="compact"
                        color="primary"
                        class="option-control"
                        v-model="settings.historyDelay.deviceEnd"
                        :disabled="!settings.isShowHistoryDelay"
                        @change="sendHistoryDelaySettings"
                      ></v-checkbox>
                    </div>

                    <div class="option-item">
                      <div class="option-label">遥控端丢包率</div>
                      <v-checkbox
                        hide-details
                        density="compact"
                        color="primary"
                        class="option-control"
                        v-model="settings.historyDelay.remotePacketLoss"
                        :disabled="!settings.isShowHistoryDelay"
                        @change="sendHistoryDelaySettings"
                      ></v-checkbox>
                    </div>
                  </div>
                  <div class="option-row">
                    <div class="option-item">
                      <div class="option-label">设备端丢包率</div>
                      <v-checkbox
                        hide-details
                        density="compact"
                        color="primary"
                        class="option-control"
                        v-model="settings.historyDelay.devicePacketLoss"
                        :disabled="!settings.isShowHistoryDelay"
                        @change="sendHistoryDelaySettings"
                      ></v-checkbox>
                    </div>
                    <div class="option-item">
                      <div class="option-label">视频抖动值</div>
                      <v-checkbox
                        hide-details
                        density="compact"
                        color="primary"
                        class="option-control"
                        v-model="settings.historyDelay.videoJitter"
                        :disabled="!settings.isShowHistoryDelay"
                        @change="sendHistoryDelaySettings"
                      ></v-checkbox>
                    </div>
                    <div class="option-item">
                      <div class="option-label">指令抖动值</div>
                      <v-checkbox
                        hide-details
                        density="compact"
                        color="primary"
                        class="option-control"
                        v-model="settings.historyDelay.commandJitter"
                        :disabled="!settings.isShowHistoryDelay"
                        @change="sendHistoryDelaySettings"
                      ></v-checkbox>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </v-card>
        </v-window-item>

        <!-- 设备网络自检视图 -->
        <v-window-item value="network-check">
          <v-card class="content-card">
            <div class="network-check-container">
              <div class="network-check-buttons">
                <v-btn
                  color="success"
                  size="x-large"
                  class="network-check-btn"
                  prepend-icon="mdi-play-circle"
                  @click="startNetworkCheck"
                >
                  自检
                </v-btn>
                <v-btn
                  color="error"
                  size="x-large"
                  class="network-check-btn"
                  prepend-icon="mdi-stop-circle"
                  @click="stopNetworkCheck"
                >
                  停止自检
                </v-btn>
                <v-btn
                  color="warning"
                  size="x-large"
                  class="network-check-btn"
                  prepend-icon="mdi-arrow-up-circle"
                  @click="previousNetworkCheck"
                >
                  上一项
                </v-btn>
                <v-btn
                  color="info"
                  size="x-large"
                  class="network-check-btn"
                  prepend-icon="mdi-arrow-down-circle"
                  @click="nextNetworkCheck"
                >
                  下一项
                </v-btn>
              </div>
            </div>
          </v-card>
        </v-window-item>
      </v-window>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref, watch } from "vue";
import { ipc } from "@/utils/ipcRenderer";
import { sendEvent2Android, sendSync2Android } from "@/utils/androidMessage";
import { registerWsHandler, unregisterWsHandler } from "@/utils/ipcMessageHandler";

// 选项卡状态
const tab = ref("delay-chart");

// 标签页配置
const tabItems = [
  { value: "delay-chart", title: "时延曲线图" },
  { value: "network-check", title: "设备网络自检" },
];

// 设置状态
const settings = ref({
  isShowCurrentDelay: false,
  isShowHistoryDelay: false,
  listDisplay: false,
  currentDelay: {
    remoteControl: false,
    deviceEnd: false,
    remotePacketLoss: false,
    devicePacketLoss: false,
    videoJitter: true,
    commandJitter: true,
  },
  historyDelay: {
    remoteControl: false,
    deviceEnd: false,
    remotePacketLoss: false,
    devicePacketLoss: false,
    videoJitter: true,
    commandJitter: true,
  },
});

// 切换当前时延曲线图显示状态
const toggleCurrentDelay = () => {
  settings.value.isShowCurrentDelay = !settings.value.isShowCurrentDelay;
  sendEvent2Android(settings.value.isShowCurrentDelay ? "currentDelay.open" : "currentDelay.close");
};

// 切换历史时延曲线图显示状态
const toggleHistoryDelay = async () => {
  settings.value.isShowHistoryDelay = !settings.value.isShowHistoryDelay;
  await sendEvent2Android(settings.value.isShowHistoryDelay ? "historyDelay.open" : "historyDelay.close");
};

const listDisplay = async () => {
  settings.value.listDisplay = !settings.value.listDisplay;
  await sendEvent2Android(settings.value.listDisplay ? "listDisplay.open" : "listDisplay.close");
};

const sendDelaySettings = async () => {
  await console.log("发送时延设置", JSON.parse(JSON.stringify(settings.value.currentDelay)));
  await sendSync2Android(11, JSON.parse(JSON.stringify(settings.value.currentDelay)));
};

const sendHistoryDelaySettings = async () => {
  console.log("发送历史时延设置");
  await sendSync2Android(12, JSON.parse(JSON.stringify(settings.value.historyDelay)));
};

// 网络自检功能
const openNetworkCheck = async () => {
  console.log("开始网络自检");
  await sendEvent2Android("networkCheck.open");
};

const closedNetworkCheck = async () => {
  console.log("关闭网络自检");
  await sendEvent2Android("networkCheck.close");
};

const startNetworkCheck = async () => {
  console.log("开始网络自检");
  await sendEvent2Android("networkCheck.start");
};

const stopNetworkCheck = async () => {
  console.log("停止网络自检");
  await sendEvent2Android("networkCheck.stop");
};

const previousNetworkCheck = async () => {
  console.log("上一项网络自检");
  await sendEvent2Android("networkCheck.previous");
};

const nextNetworkCheck = async () => {
  console.log("下一项网络自检");
  await sendEvent2Android("networkCheck.next");
};

const closedDelay = () => {
  if (settings.value.isShowCurrentDelay) toggleCurrentDelay();
  if (settings.value.isShowHistoryDelay) toggleHistoryDelay();
};

watch(
  () => tab.value,
  (newValue, oldValue) => {
    if (newValue === "delay-chart") {
      closedNetworkCheck();
    }
    if (newValue === "network-check") {
      closedDelay();
      openNetworkCheck();
    }
  }
);

// 处理WebSocket消息
const handleWsMessage = (event, msg) => {
  console.log(msg);
  const { type, page, payload } = msg.jsonData;
  if (type === "event") {
    console.log("收到listDisplay设置");
    if (payload.route === "listDisplay.open") {
      settings.value.listDisplay = true;
    } else if (payload.route === "listDisplay.close") {
      settings.value.listDisplay = false;
    } else if (payload.route === "currentDelay.open") {
      settings.value.isShowCurrentDelay = true;
    } else if (payload.route === "currentDelay.close") {
      settings.value.isShowCurrentDelay = false;
    } else if (payload.route === "historyDelay.open") {
      settings.value.isShowHistoryDelay = true;
    } else if (payload.route === "historyDelay.close") {
      settings.value.isShowHistoryDelay = false;
    }
  } else if (type === "sync" && page === 11) {
    settings.value.currentDelay = payload;
    settings.value.isShowCurrentDelay = true;
  } else if (type === "sync" && page === 12) {
    settings.value.historyDelay = payload;
    settings.value.isShowHistoryDelay = true;
  }
};

let handlerId = null;

onMounted(async () => {
  // 注册WebSocket消息处理函数
  handlerId = registerWsHandler("*", handleWsMessage);
});

onUnmounted(() => {
  // 注销WebSocket消息处理函数
  if (handlerId) {
    unregisterWsHandler(handlerId);
  }
  closedDelay();
});
</script>

<style scoped>
.tabs-container {
  width: 100px;
  height: calc(100vh - var(--app-bar-height));
  background-color: rgb(var(--v-theme-surface));
  border-right: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.network-tabs :deep(.v-tab) {
  min-height: 200px;
  height: calc((100vh - var(--app-bar-height)) / 2);
  font-size: 24px;
  justify-content: center;
  padding: 0;
  border-radius: 0;
  transition: background-color 0.3s ease;
  writing-mode: vertical-lr;
  text-orientation: upright;
}

.network-tabs :deep(.v-tab--selected) {
  background-color: rgba(var(--v-theme-primary), 0.1);
}

.network-tabs :deep(.v-tab:hover) {
  background-color: rgba(var(--v-theme-primary), 0.05);
}

.content-container {
  flex: 1;
  height: calc(100vh - var(--app-bar-height));
  overflow-y: auto;
  background-color: rgb(var(--v-theme-surface));
}

.content-card {
  height: calc(100vh - var(--app-bar-height));
  border-radius: 0;
  padding: 24px;
  background-color: transparent;
}

.section-container {
  border: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
  border-radius: 12px;
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 25px;
  background-color: rgba(var(--v-theme-surface-variant), 0.1);
  border-bottom: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.section-title {
  font-size: 28px;
  font-weight: 600;
}

.section-content {
  padding: 25px;
}

.chart-options {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.option-row {
  display: flex;
  justify-content: space-between;
  gap: 40px;
}

.option-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
  padding: 15px 20px;
  border: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
  border-radius: 8px;
}

.option-label {
  font-size: 22px;
  font-weight: 500;
}

.list-display-checkbox :deep(.v-label) {
  font-size: 18px;
}

.network-check-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 40px;
}

.network-check-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 40px;
  width: 100%;
  max-width: 800px;
}

.network-check-btn {
  height: 120px;
  font-size: 24px;
  font-weight: 500;
  border-radius: 12px;
}
</style>
