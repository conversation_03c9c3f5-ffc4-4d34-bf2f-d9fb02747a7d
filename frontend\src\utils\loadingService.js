import { ipc } from "./ipcRenderer";

/**
 * 全局加载服务
 * 提供全局加载状态控制功能
 */
class LoadingService {
  /**
   * 显示加载
   * @param {Object} config - 加载配置
   * @param {string} config.text - 加载文本
   * @param {string} config.type - 加载类型: 'default', 'circular', 'linear'
   * @param {string} config.color - 加载颜色
   * @param {string} config.textColor - 文本颜色
   * @param {number} config.size - 圆形加载器大小
   * @param {number} config.width - 圆形加载器宽度
   * @param {number} config.height - 线性加载器高度
   * @param {number} config.opacity - 遮罩透明度
   * @param {number} config.zIndex - z-index值
   * @param {string} config.scrimColor - 遮罩颜色
   * @param {boolean} config.customTemplate - 是否使用自定义模板
   * @param {boolean} config.autoHide - 是否自动隐藏
   * @param {number} config.duration - 自动隐藏时间(毫秒)
   * @returns {Object} - 控制对象，包含hide和updateText方法
   */
  show(config = {}) {
    const fullConfig = {
      action: 'show',
      ...config
    };
    
    ipc.invoke("controller/message/sendLoading", fullConfig);
    
    // 返回控制对象
    return {
      hide: () => this.hide(),
      updateText: (text) => this.updateText(text)
    };
  }
  
  /**
   * 隐藏加载
   */
  hide() {
    ipc.invoke("controller/message/sendLoading", { action: 'hide' });
  }
  
  /**
   * 更新加载文本
   * @param {string} text - 新的加载文本
   */
  updateText(text) {
    ipc.invoke("controller/message/sendLoading", { 
      action: 'updateText',
      text
    });
  }
  
  /**
   * 显示默认加载
   * @param {string} text - 加载文本
   * @param {Object} options - 其他配置选项
   * @returns {Object} - 控制对象
   */
  default(text, options = {}) {
    return this.show({
      text,
      type: 'default',
      ...options
    });
  }
  
  /**
   * 显示圆形加载
   * @param {string} text - 加载文本
   * @param {Object} options - 其他配置选项
   * @returns {Object} - 控制对象
   */
  circular(text, options = {}) {
    return this.show({
      text,
      type: 'circular',
      ...options
    });
  }
  
  /**
   * 显示线性加载
   * @param {string} text - 加载文本
   * @param {Object} options - 其他配置选项
   * @returns {Object} - 控制对象
   */
  linear(text, options = {}) {
    return this.show({
      text,
      type: 'linear',
      ...options
    });
  }
  
  /**
   * 创建一个自动隐藏的加载
   * @param {string} text - 加载文本
   * @param {number} duration - 显示时长(毫秒)
   * @param {Object} options - 其他配置选项
   * @returns {Object} - 控制对象
   */
  autoHide(text, duration = 3000, options = {}) {
    return this.show({
      text,
      autoHide: true,
      duration,
      ...options
    });
  }
}

// 创建单例实例
const loadingService = new LoadingService();

export default loadingService;
