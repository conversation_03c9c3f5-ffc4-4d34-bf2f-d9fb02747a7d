<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Demo</title>
</head>
<body>
    <h2>WebSocket 演示</h2>
    <div>
        <input type="text" id="messageInput" placeholder="输入消息">
        <button onclick="sendMessage()">发送</button>
    </div>
    <div id="status" style="margin: 20px 0; color: #666;"></div>
    <div id="output" style="height: 300px; overflow-y: scroll; border: 1px solid #ccc; padding: 10px;"></div>

    <script>
        // 使用公共测试服务器（注意：仅用于演示，不建议生产环境使用）
        // const socket = new WebSocket('wss://oms.apps.builderx.com/api/v1/ws/user?authorization=Bearer%20u-399e8f8fcccefb38aa87f04377b924bf');
        const socket = new WebSocket('ws://**************:20009');

        // 连接建立时触发
        socket.onopen = function(event) {
            updateStatus('已连接到服务器');
            addMessage('系统', '连接已建立', 'system');
        };ww

        // 收到消息时触发
        socket.onmessage = function(event) {
            addMessage('服务器', event.data, 'received');
        };

        // 连接关闭时触发
        socket.onclose = function(event) {
            updateStatus('连接已关闭');
            addMessage('系统', '连接已关闭', 'system');
        };

        // 错误处理
        socket.onerror = function(error) {
            updateStatus('连接错误: ' + error.message);
        };

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (message) {
                socket.send(message);
                addMessage('我', message, 'sent');
                input.value = '';
            }
        }

        function addMessage(sender, message, type) {
            const output = document.getElementById('output');
            const messageDiv = document.createElement('div');
            messageDiv.innerHTML = `<strong>${sender}:</strong> ${message}`;
            messageDiv.style.color = type === 'sent' ? 'blue' : type === 'received' ? 'green' : 'gray';
            output.appendChild(messageDiv);
            output.scrollTop = output.scrollHeight;
        }

        function updateStatus(status) {
            document.getElementById('status').textContent = '当前状态: ' + status;
        }

        // 允许通过回车键发送消息
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    </script>
</body>
</html>
