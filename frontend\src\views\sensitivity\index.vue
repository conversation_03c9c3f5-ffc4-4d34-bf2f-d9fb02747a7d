<template>
  <div class="flex">
    <!-- 左侧垂直选项卡 -->
    <div class="tabs-container">
      <v-tabs v-model="tab" color="primary" direction="vertical" class="action-tabs">
        <v-tab v-for="item in tabItems" :key="item.value" :value="item.value" class="action-tab" style="height: 200px">
          {{ item.title }}
        </v-tab>
      </v-tabs>
    </div>

    <!-- 右侧功能区域 -->
    <div class="content-container">
      <v-window v-model="tab">
        <!-- 方案一 -->
        <v-window-item value="plan1">
          <v-card class="content-card">
            <!-- 使用抽离的挖掘机手柄配置组件 -->
            <ExcavatorHandleConfig
              v-if="Object.keys(curHandleForm).length > 0"
              v-model:curSelectedKey="pageData.curSelectedKey"
              v-model:checkList="pageData.checkList"
              :handleForm="curHandleForm"
              @select-item="selectItem"
            />
          </v-card>
        </v-window-item>

        <!-- 方案二 -->
        <v-window-item value="plan2">
          <v-card class="content-card">
            <!-- 方案二内容与方案一相同，只是数据不同 -->
            <ExcavatorHandleConfig
              v-if="Object.keys(curHandleForm).length > 0"
              v-model:curSelectedKey="pageData.curSelectedKey"
              v-model:checkList="pageData.checkList"
              :handleForm="curHandleForm"
              @select-item="selectItem"
            />
          </v-card>
        </v-window-item>

        <!-- 方案三 -->
        <v-window-item value="plan3">
          <v-card class="content-card">
            <!-- 方案三内容与方案一相同，只是数据不同 -->
            <ExcavatorHandleConfig
              v-if="Object.keys(curHandleForm).length > 0"
              v-model:curSelectedKey="pageData.curSelectedKey"
              v-model:checkList="pageData.checkList"
              :handleForm="curHandleForm"
              @select-item="selectItem"
            />
          </v-card>
        </v-window-item>
      </v-window>
    </div>

    <!-- 右侧控制面板 -->
    <div class="control-panel">
      <div class="control-wrap w-[258px]">
        <!-- 曲线图展示 -->
        <div class="input-wrap flex flex-column mt-1 w-[258px]">
          <div class="chart-wrap">
            <div ref="webChartRef" style="width: 258px; height: 258px"></div>
          </div>
        </div>
        <div class="flex flex-row align-center mt-6">
          <span class="w-[60px]">极值:</span>
          <v-range-slider
            v-model="pageData.minMaxValue"
            :max="100"
            :min="0"
            :min-width="20"
            :step="1"
            thumb-label="always"
            hide-details
            @update:model-value="setChartData"
          >
          </v-range-slider>
        </div>
        <div class="flex flex-row align-center mt-6">
          <span class="w-[60px]">灵敏度:</span>
          <v-slider
            v-model="pageData.sensitivity"
            :min="-1"
            :max="1"
            :step="0.1"
            thumb-label="always"
            hide-details
            @update:model-value="setChartData"
          >
          </v-slider>
        </div>
        <div class="action-buttons align-center mt-10">
          <v-btn class="w-[68px] action-btn mr-2" @click="resetAll">重置所有</v-btn>
          <v-btn class="w-[68px] action-btn mr-2" @click="resetCur">重置当前</v-btn>
          <v-btn class="w-[68px] action-btn mr-2" @click="syncAll">同步全部</v-btn>
          <v-btn class="w-[68px] action-btn" @click="selectAll">
            {{ pageData.checkList.length === Object.keys(curHandleForm).length ? "取消全选" : "选中全部" }}
          </v-btn>
        </div>
        <div class="align-center mt-3 mb-1">
          <v-btn class="w-full action-btn mr-2" @click="handleSubmit">保存并应用</v-btn>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed, onMounted, onBeforeMount } from "vue";
import { useRouter } from "vue-router";
import { ipc } from "@/utils/ipcRenderer";
import * as echarts from "echarts";
import notificationService from "@/utils/notificationService";
import ExcavatorHandleConfig from "./components/ExcavatorHandleConfig.vue";



const planIndexMap = {
  plan1: 0,
  plan2: 1,
  plan3: 2,
};
// 选项卡状态
const tab = ref("plan1");
const router = useRouter();

// 图表引用
const webChartRef = ref(null);
let WebChart = null;

// 页面数据
const pageData = reactive({
  checkList: [],
  curSelectedKey: "",
  sensitivity: 0,
  minMaxValue: [0, 100],
});

const handleData = ref([]);

// 操作手柄表单
const curHandleForm = reactive({});

// 标签页配置
const tabItems = [
  { value: "plan1", title: "方案一" },
  { value: "plan2", title: "方案二" },
  { value: "plan3", title: "方案三" },
];

// 选择操作项
const selectItem = (key) => {
  pageData.curSelectedKey = key;
  pageData.minMaxValue[0] = curHandleForm[key].low_limit;
  pageData.minMaxValue[1] = curHandleForm[key].up_limit;
  pageData.sensitivity = curHandleForm[key].sensitivity;
  WebChart.setOption({
    series: [{ data: chartCompute(pageData.minMaxValue[0], pageData.minMaxValue[1], pageData.sensitivity) }],
  });
};

// 重置当前
const resetCur = () => {
  let key = pageData.curSelectedKey;
  if (!key) return notificationService.warning("请选择需要重置的操作项");
  curHandleForm[key].low_limit = 0;
  curHandleForm[key].up_limit = 100;
  curHandleForm[key].sensitivity = 0;
  resetChart();
};

// 重置所有
const resetAll = () => {
  for (const key in curHandleForm) {
    if (Object.prototype.hasOwnProperty.call(curHandleForm, key)) {
      const item = curHandleForm[key];
      item.low_limit = 0;
      item.up_limit = 100;
      item.sensitivity = 0;
    }
  }
  resetChart();
};

// 同步全部
const syncAll = () => {
  if (!pageData.curSelectedKey) return notificationService.warning("请选择需要重置的操作项");
  for (const key in curHandleForm) {
    if (Object.prototype.hasOwnProperty.call(curHandleForm, key)) {
      const item = curHandleForm[key];
      item.low_limit = pageData.minMaxValue[0];
      item.up_limit = pageData.minMaxValue[1];
      item.sensitivity = pageData.sensitivity;
    }
  }
};

// 选中全部
const selectAll = () => {
  if (pageData.checkList.length === Object.keys(curHandleForm).length) {
    pageData.checkList = [];
  } else {
    pageData.checkList = Object.keys(curHandleForm);
  }
};

// 重置图表
const resetChart = () => {
  pageData.minMaxValue = [0, 100];
  pageData.sensitivity = 0;

  WebChart.setOption({
    series: [{ data: chartCompute(0, 100, 0) }],
  });
};

// 返回
const goBack = () => {
  router.push({ path: "/settings" });
};

// 获取车辆详情
const getVehiclesHandleData = async () => {
  try {
    handleData.value = await ipc.invoke("global-state:get", "handleConfig.handleSettingList");
    setCurHandleData();
  } catch (error) {
    console.error("获取车辆详情失败", error);
  }
};

// 设置当前方案手柄数据
const setCurHandleData = () => {
  const { isPublic, isUse, step_switch, ...curHandle } = handleData.value[planIndexMap[tab.value]];
  Object.assign(curHandleForm, curHandle);
  if (pageData.curSelectedKey) selectItem(pageData.curSelectedKey);
};

// 提交表单
const handleSubmit = async () => {
  try {
    handleData.value.forEach((item, index) => {
      if (index === planIndexMap[tab.value]) {
        Object.assign(item, curHandleForm);
        item.isUse = true;
      } else {
        item.isUse = false;
      }
    });
    await ipc.invoke("global-state:set", "handleConfig.handleSettingList", JSON.parse(JSON.stringify(handleData.value)));
    notificationService.success("操作成功");
  } catch (error) {
    console.error("更新手柄信息失败", error);
    notificationService.error("操作失败");
  }
};

// 图表计算
const chartCompute = (a = 0, b = 100, c = 0) => {
  let R_min = Math.abs((Math.pow(a, 2) - Math.pow(b, 2) + Math.pow(100, 2)) / (2 * a - 2 * b) - b);
  let R = R_min + 1000 * (1 - Math.abs(c));
  let m = (Math.pow(100, 2) + Math.pow(b, 2) - Math.pow(a, 2)) / 200;
  let n = (a - b) / 100;
  let p = (m * n - a) / Math.sqrt(Math.pow(n, 2) + 1);
  let y1 =
    (Math.sqrt(Math.pow(R, 2) - Math.pow(a, 2) - Math.pow(m, 2) + Math.pow(p, 2)) - p) / Math.sqrt(Math.pow(n, 2) + 1);
  let x1 = m + n * y1;
  let y2 =
    (-Math.sqrt(Math.pow(R, 2) - Math.pow(a, 2) - Math.pow(m, 2) + Math.pow(p, 2)) - p) / Math.sqrt(Math.pow(n, 2) + 1);
  let x2 = m + n * y2;

  let x, y;

  if (c === 0) {
    // 直线
    x = linspace(0, 100, 1000);
    y = x.map((xi) => ((b - a) * xi) / 100 + a);
  } else if (c < 0) {
    const cx = x1,
      cy = y1;
    x = linspace(0, 100, 1000);
    y = x.map((xi) => -Math.sqrt(R ** 2 - (xi - cx) ** 2) + cy);
  } else {
    const cx = x2,
      cy = y2;
    x = linspace(0, 100, 1000);
    y = x.map((xi) => Math.sqrt(R ** 2 - (xi - cx) ** 2) + cy);
  }

  let data = [];
  x.forEach((item, index) => {
    data.push([item, y[index]]);
  });

  return data;
};

// 线性空间
const linspace = (startValue, stopValue, cardinality) => {
  const arr = [];
  const step = (stopValue - startValue) / (cardinality - 1);
  for (let i = 0; i < cardinality; i++) {
    arr.push(startValue + step * i);
  }
  return arr;
};

// 设置图表数据
const setChartData = () => {
  console.log(pageData.curSelectedKey);
  if (pageData.curSelectedKey) {
    if (pageData.checkList.length > 1 && pageData.checkList.includes(pageData.curSelectedKey)) {
      pageData.checkList.forEach((item) => {
        curHandleForm[item].low_limit = pageData.minMaxValue[0];
        curHandleForm[item].up_limit = pageData.minMaxValue[1];
        curHandleForm[item].sensitivity = pageData.sensitivity;
      });
    } else {
      curHandleForm[pageData.curSelectedKey].low_limit = pageData.minMaxValue[0];
      curHandleForm[pageData.curSelectedKey].up_limit = pageData.minMaxValue[1];
      curHandleForm[pageData.curSelectedKey].sensitivity = pageData.sensitivity;
    }
    WebChart.setOption({
      series: [{ data: chartCompute(pageData.minMaxValue[0], pageData.minMaxValue[1], pageData.sensitivity) }],
    });
  } else {
    notificationService.warning("请选择需要重置的操作项");
  }
};

// 初始化图表
const initChart = () => {
  if (!webChartRef.value) return;

  WebChart = echarts.init(webChartRef.value);
  const option = {
    grid: {
      left: 10,
      right: 10,
      bottom: 20,
      top: 10,
      containLabel: true,
    },
    xAxis: {
      type: "value",
      min: 0,
      max: 100,
      splitLine: {
        lineStyle: {
          type: "dashed",
        },
      },
    },
    yAxis: {
      type: "value",
      min: 0,
      max: 100,
      splitLine: {
        lineStyle: {
          type: "dashed",
        },
      },
    },
    series: [
      {
        data: chartCompute(0, 100, 0),
        type: "line",
        smooth: true,
        symbol: "none",
        lineStyle: {
          width: 3,
          color: "#ff5f00",
        },
      },
    ],
  };
  WebChart.setOption(option);
};

// 组件挂载后初始化
onMounted(() => {
  initChart();
  getVehiclesHandleData();
});

watch(
  () => tab.value,
  (newValue, oldValue) => {
    setCurHandleData();
  }
);
</script>

<style scoped>
/* 左侧选项卡样式 */
.tabs-container {
  width: 100px;
  height: calc(100vh - var(--app-bar-height));
  background-color: rgb(var(--v-theme-surface));
  border-right: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.action-tabs {
  height: 100%;
}

.action-tabs :deep(.v-tab) {
  min-height: 100px;
  font-size: 24px;
  justify-content: center;
  padding: 0;
  border-radius: 0;
  transition: background-color 0.3s ease;
  writing-mode: vertical-lr;
  text-orientation: upright;
}

.action-tabs :deep(.v-tab--selected) {
  background-color: rgba(var(--v-theme-primary), 0.1);
}

.action-tabs :deep(.v-tab:hover) {
  background-color: rgba(var(--v-theme-primary), 0.05);
}

/* 中间内容区域样式 */
.content-container {
  flex: 1;
  overflow-y: auto;
  background-color: rgb(var(--v-theme-surface));
}

.content-card {
  height: 100%;
  border-radius: 0;
  padding: 24px;
  background-color: transparent;
}

.action-btn {
  font-size: 20px;
  height: 60px;
  width: 100%;
  border: 2px solid rgba(var(--v-border-color), var(--v-border-opacity));
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
  position: relative;
}

/* 曲线图区域样式 */
.action-chart {
  height: 180px;
  border: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
  margin: 0 20px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 100 100"><path d="M0,100 Q30,100 40,80 T70,50 T100,0" fill="none" stroke="black" stroke-width="2"/></svg>');
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
}

/* 右侧控制面板样式 */
.control-panel {
  width: 300px;
  padding: 20px;
  border-left: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
  display: flex;
  flex-direction: column;
  background-color: rgb(var(--v-theme-surface));
}

:deep(.v-btn--block) {
  min-width: 0;
  flex: 0 0 auto;
}

.control-buttons {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
}

.control-btn {
  font-size: 20px;
  width: 120px;
  height: 60px;
  padding: 0;
}

.control-values {
  margin-bottom: 20px;
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}
</style>
