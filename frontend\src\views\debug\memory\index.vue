<template>
  <v-container>
    <v-card class="memory-monitor">
      <v-card-title class="d-flex justify-space-between align-center">
        <span>内存监控</span>
        <div>
          <v-btn icon @click="refreshData">
            <v-icon>mdi-refresh</v-icon>
          </v-btn>
          <!-- 清除数据 -->
          <v-btn icon @click="clearData">
            <v-icon>mdi-delete</v-icon>
          </v-btn>
        </div>
      </v-card-title>

      <v-card-text>
        <v-row>
          <v-col cols="12" md="6">
            <v-card outlined>
              <v-card-title class="subtitle-1">主进程内存使用</v-card-title>
              <v-card-text>
                <v-row v-if="currentMemory">
                  <v-col cols="6">
                    <div class="text-subtitle-2">RSS</div>
                    <div class="text-h6">{{ currentMemory.rss }} MB</div>
                  </v-col>
                  <v-col cols="6">
                    <div class="text-subtitle-2">堆内存使用</div>
                    <div class="text-h6">{{ currentMemory.heapUsed }} MB</div>
                  </v-col>
                  <v-col cols="6">
                    <div class="text-subtitle-2">堆内存总量</div>
                    <div class="text-h6">{{ currentMemory.heapTotal }} MB</div>
                  </v-col>
                  <v-col cols="6">
                    <div class="text-subtitle-2">外部内存</div>
                    <div class="text-h6">{{ currentMemory.external }} MB</div>
                  </v-col>
                </v-row>
                <v-skeleton-loader v-else type="card" />
              </v-card-text>
            </v-card>
          </v-col>

          <v-col cols="12" md="6">
            <v-card outlined>
              <v-card-title class="subtitle-1">系统内存使用</v-card-title>
              <v-card-text>
                <v-row v-if="systemMemory">
                  <v-col cols="6">
                    <div class="text-subtitle-2">总内存</div>
                    <div class="text-h6">{{ systemMemory.total }} MB</div>
                  </v-col>
                  <v-col cols="6">
                    <div class="text-subtitle-2">已用内存</div>
                    <div class="text-h6">{{ systemMemory.used }} MB</div>
                  </v-col>
                  <v-col cols="6">
                    <div class="text-subtitle-2">可用内存</div>
                    <div class="text-h6">{{ systemMemory.free }} MB</div>
                  </v-col>
                  <v-col cols="6">
                    <div class="text-subtitle-2">使用率</div>
                    <div class="text-h6">{{ systemMemory.usagePercent }}%</div>
                  </v-col>
                </v-row>
                <v-skeleton-loader height="134" v-else type="card" />
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>

        <v-row class="mt-4">
          <v-col cols="12">
            <v-card outlined>
              <v-card-title class="subtitle-1">内存使用趋势</v-card-title>
              <v-card-text class="mx-10">
                <div class="chart-container">
                  <div v-if="memoryData.length === 0" class="text-center pa-4">暂无数据</div>
                  <div v-else>
                    <div class="d-flex justify-space-between align-center mb-2">
                      <div class="text-subtitle-2">RSS 内存使用趋势 (MB)</div>
                      <div class="d-flex align-center">
                        <div class="text-caption mr-2">已收集 {{ memoryData.length }} 个数据点</div>
                        <v-chip v-if="currentMemory" size="small" :color="getRssColor(currentMemory.rss)" class="ml-2">
                          当前: {{ currentMemory.rss }} MB
                        </v-chip>
                      </div>
                    </div>
                    <v-sparkline
                      class="h-[200px] mx-auto"
                      :gradient="['#f72047', '#ffd200', '#1feaea']"
                      :model-value="rssValues"
                      :line-width="1"
                      :padding="rssValues.length / 5"
                      stroke-linecap="round"
                      smooth
                    >
                      <template v-slot:label="item">
                        {{ item.value }}
                      </template>
                    </v-sparkline>
                    <div class="d-flex justify-space-between mt-2">
                      <div class="text-caption">最小值: {{ minRss }} MB</div>
                      <div class="text-caption">最大值: {{ maxRss }} MB</div>
                      <div class="text-caption">平均值: {{ avgRss }} MB</div>
                    </div>

                    <div class="d-flex justify-space-between align-center mt-6 mb-2">
                      <div class="text-subtitle-2">堆内存使用趋势 (MB)</div>
                      <div class="d-flex align-center">
                        <div class="text-caption mr-2">趋势分析</div>
                        <v-chip
                          v-if="currentMemory"
                          size="small"
                          :color="getHeapColor(currentMemory.heapUsed)"
                          class="ml-2"
                        >
                          当前: {{ currentMemory.heapUsed }} MB
                        </v-chip>
                      </div>
                    </div>
                    <v-sparkline
                      class="h-[200px] mx-auto"
                      :gradient="['#f72047', '#ffd200', '#1feaea']"
                      :model-value="heapUsedValues"
                      :line-width="1"
                      :padding="rssValues.length / 5"
                      stroke-linecap="round"
                      smooth
                    >
                      <template v-slot:label="item">
                        {{ item.value }}
                      </template>
                    </v-sparkline>
                    <div class="d-flex justify-space-between mt-2">
                      <div class="text-caption">最小值: {{ minHeapUsed }} MB</div>
                      <div class="text-caption">最大值: {{ maxHeapUsed }} MB</div>
                      <div class="text-caption">平均值: {{ avgHeapUsed }} MB</div>
                    </div>
                  </div>
                </div>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
        <v-row>
          {{ allData }}
        </v-row>

        <!-- <v-row class="mt-4">
        <v-col cols="12">
          <v-card outlined>
            <v-card-title class="subtitle-1">监控设置</v-card-title>
            <v-card-text>
              <v-row>
                <v-col cols="12" md="6">
                  <v-switch v-model="isMonitoring" label="启用内存监控" @change="toggleMonitoring"></v-switch>
                </v-col>
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model.number="monitoringInterval"
                    label="监控间隔 (毫秒)"
                    type="number"
                    :disabled="isMonitoring"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model.number="alertThreshold"
                    label="警告阈值 (MB)"
                    type="number"
                    :disabled="isMonitoring"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="6">
                  <v-switch v-model="autoHeapDump" label="自动生成堆快照" :disabled="isMonitoring"></v-switch>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="12" class="d-flex justify-end">
                  <v-btn color="primary" @click="applySettings" :disabled="isMonitoring"> 应用设置 </v-btn>
                  <v-btn class="ml-2" color="warning" @click="generateHeapSnapshot"> 生成堆快照 </v-btn>
                  <v-btn class="ml-2" color="error" @click="clearData"> 清除数据 </v-btn>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row> -->
      </v-card-text>
    </v-card>
    <BackButton />
  </v-container>
</template>

<script setup>
import BackButton from "@/components/BackButton/index.vue";
import { ref, computed, onMounted, onBeforeUnmount } from "vue";
import { ipc } from "@/utils/ipcRenderer";

// 响应式状态
const currentMemory = ref(null);
const systemMemory = ref(null);
const memoryData = ref([]);
const isMonitoring = ref(false);
const monitoringInterval = ref(60000);
const alertThreshold = ref(500);
const autoHeapDump = ref(false);
let refreshInterval = null;

// 计算属性 - 提取RSS和堆内存使用值用于图表
const rssValues = computed(() => {
  return memoryData.value.map((data) => {
    return data.rss;
  });
});

const heapUsedValues = computed(() => {
  return memoryData.value.map((data) => {
    return data.heapUsed;
  });
});

// 计算RSS统计数据
const minRss = computed(() => {
  if (rssValues.value.length === 0) return 0;
  return Math.min(...rssValues.value);
});

const maxRss = computed(() => {
  if (rssValues.value.length === 0) return 0;
  return Math.max(...rssValues.value);
});

const avgRss = computed(() => {
  if (rssValues.value.length === 0) return 0;
  const sum = rssValues.value.reduce((acc, val) => acc + val, 0);
  return Math.round(sum / rssValues.value.length);
});

// 计算堆内存统计数据
const minHeapUsed = computed(() => {
  if (heapUsedValues.value.length === 0) return 0;
  return Math.min(...heapUsedValues.value);
});

const maxHeapUsed = computed(() => {
  if (heapUsedValues.value.length === 0) return 0;
  return Math.max(...heapUsedValues.value);
});

const avgHeapUsed = computed(() => {
  if (heapUsedValues.value.length === 0) return 0;
  const sum = heapUsedValues.value.reduce((acc, val) => acc + val, 0);
  return Math.round(sum / heapUsedValues.value.length);
});

// 辅助函数 - 根据内存使用情况返回颜色
const getRssColor = (value) => {
  if (value > alertThreshold.value * 1.5) return "error";
  if (value > alertThreshold.value) return "warning";
  if (value > avgRss.value * 1.2) return "orange";
  return "success";
};

const getHeapColor = (value) => {
  if (value > alertThreshold.value * 0.8) return "error";
  if (value > alertThreshold.value * 0.6) return "warning";
  if (value > avgHeapUsed.value * 1.2) return "orange";
  return "success";
};

// 事件
const emit = defineEmits(["show-notification"]);

// 方法
const getStatus = async () => {
  try {
    const status = await ipc.invoke("controller/memory/getStatus");
    isMonitoring.value = status.isMonitoring;
    monitoringInterval.value = status.interval;
    alertThreshold.value = status.alertThreshold;
    autoHeapDump.value = status.autoHeapDump;
  } catch (error) {
    console.error("Failed to get memory monitor status:", error);
  }
};

let allData = ref({});
const refreshData = async () => {
  try {
    // 获取当前内存使用情况
    const currentUsage = await ipc.invoke("controller/memory/getCurrentMemoryUsage");
    currentMemory.value = currentUsage;

    // 将当前内存使用情况添加到图表数据中
    if (currentUsage) {
      // 添加到内存数据数组用于图表显示
      memoryData.value.push(currentUsage);

      // 限制数据点数量，避免内存占用过多
      if (memoryData.value.length > 50) {
        memoryData.value.shift();
      }
    }

    // 获取内存监控数据
    const data = await ipc.invoke("controller/memory/getData");
    allData.value = data[data.length - 1];

    // 获取最新的系统内存数据
    if (data.length > 0) {
      systemMemory.value = data[data.length - 1].system;
    }
  } catch (error) {
    console.error("Failed to refresh memory data:", error);
  }
};

const updateMemoryData = (data) => {
  currentMemory.value = data.mainProcess;
  systemMemory.value = data.system;
};

// 注意：以下函数已被注释掉，因为当前UI中没有使用它们
// 如果需要重新启用监控设置功能，可以取消注释

// const toggleMonitoring = async () => {
//   try {
//     if (isMonitoring.value) {
//       await ipc.invoke("controller/memory/startMonitoring", {
//         interval: monitoringInterval.value,
//         alertThreshold: alertThreshold.value,
//         autoHeapDump: autoHeapDump.value,
//       });
//     } else {
//       await ipc.invoke("controller/memory/stopMonitoring");
//     }
//   } catch (error) {
//     console.error("Failed to toggle memory monitoring:", error);
//     isMonitoring.value = !isMonitoring.value; // 恢复状态
//   }
// };

// const applySettings = async () => {
//   try {
//     await ipc.invoke("controller/memory/stopMonitoring");
//     await ipc.invoke("controller/memory/startMonitoring", {
//       interval: monitoringInterval.value,
//       alertThreshold: alertThreshold.value,
//       autoHeapDump: autoHeapDump.value,
//     });
//     isMonitoring.value = true;
//   } catch (error) {
//     console.error("Failed to apply memory monitor settings:", error);
//   }
// };

// const generateHeapSnapshot = async () => {
//   try {
//     const result = await ipc.invoke("controller/memory/generateHeapSnapshot");
//     if (result) {
//       emit("show-notification", {
//         type: "success",
//         message: `堆快照已生成: ${result}`,
//       });
//     } else {
//       emit("show-notification", {
//         type: "error",
//         message: "生成堆快照失败，请检查是否安装了堆快照模块",
//       });
//     }
//   } catch (error) {
//     console.error("Failed to generate heap snapshot:", error);
//     emit("show-notification", {
//       type: "error",
//       message: `生成堆快照失败: ${error.message}`,
//     });
//   }
// };

const clearData = async () => {
  try {
    await ipc.invoke("controller/memory/clearData");
    memoryData.value = [];
    emit("show-notification", {
      type: "success",
      message: "内存监控数据已清除",
    });
  } catch (error) {
    console.error("Failed to clear memory data:", error);
    emit("show-notification", {
      type: "error",
      message: `清除数据失败: ${error.message}`,
    });
  }
};

// 生命周期钩子
onMounted(() => {
  getStatus();
  refreshData();

  // 设置自动刷新
  refreshInterval = setInterval(() => {
    refreshData();
  }, 10000); // 每10秒刷新一次

  // 监听内存警告
  ipc.on("memory-monitor:alert", (_, data) => {
    emit("show-notification", {
      type: "warning",
      message: data.message,
    });
  });

  // 监听内存数据更新
  ipc.on("memory-monitor:update", (_, data) => {
    updateMemoryData(data);
  });
});

onBeforeUnmount(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval);
  }

  // 移除事件监听
  ipc.removeAllListeners("memory-monitor:alert");
  ipc.removeAllListeners("memory-monitor:update");
});
</script>

<style scoped>
.memory-monitor {
  width: 100%;
}
.chart-container {
  width: 90%;
  position: relative;
  margin: 0 auto;
}
.v-sparkline {
  border-radius: 4px;
  padding: 12px;
  background-color: rgba(0, 0, 0, 0.02);
}
</style>
