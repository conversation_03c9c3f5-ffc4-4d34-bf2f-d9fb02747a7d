<template>
  <div class="flex">
    <div class="tabs-container">
      <v-tabs v-model="tab" color="primary" direction="vertical" class="action-tabs">
        <v-tab v-for="item in tabItems" :key="item.value" :value="item.value" class="action-tab" style="height: 90px">
          {{ item.title }}
        </v-tab>
      </v-tabs>
    </div>
    <div class="content-container">
      <v-window v-model="tab">
        <v-window-item value="deviceData">
          <v-card class="content-card">
            <DeviceData />
          </v-card>
        </v-window-item>
        <v-window-item value="androidSettings">
          <v-card class="content-card">
            <AndroidSettings />
          </v-card>
        </v-window-item>
        <v-window-item value="controlPanel">
          <v-card class="content-card">
            <ControlPanel />
          </v-card>
        </v-window-item>
        <v-window-item value="devicePanel">
          <v-card class="content-card">
            <ControlPanel />
          </v-card>
        </v-window-item>
        <v-window-item value="otherFunctions">
          <v-card class="content-card">
            <OtherFunction />
          </v-card>
        </v-window-item>
      </v-window>
    </div>
    <BackButton />
  </div>
</template>

<script setup>
import BackButton from "@/components/BackButton/index.vue";
import ControlPanel from "./components/ControlPanel.vue";
import AndroidSettings from "./components/AndroidSettings.vue";
import DeviceData from "./components/DeviceData.vue";
import OtherFunction from "./components/OtherFunction.vue";

import { ref } from "vue";
const tabItems = [
  { value: "deviceData", title: "数据展示" },
  { value: "androidSettings", title: "安卓设置" },
  { value: "controlPanel", title: "操作台采集板" },
  { value: "devicePanel", title: "设备端采集版" },
  { value: "otherFunctions", title: "其他功能" },
];

const tab = ref("deviceData");
</script>
<style scoped lang="scss">
/* 左侧选项卡样式 */
.tabs-container {
  width: 220px;
  height: calc(100vh);
  background-color: rgb(var(--v-theme-surface));
  border-right: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.action-tabs {
  height: 100%;
}

.action-tabs :deep(.v-tab) {
  min-height: 50px;
  font-size: 24px;
  justify-content: center;
  padding: 0;
  border-radius: 0;
  transition: background-color 0.3s ease;
}

.action-tabs :deep(.v-tab--selected) {
  background-color: rgba(var(--v-theme-primary), 0.1);
}

.action-tabs :deep(.v-tab:hover) {
  background-color: rgba(var(--v-theme-primary), 0.05);
}

/* 中间内容区域样式 */
.content-container {
  flex: 1;
  overflow-y: auto;
  background-color: rgb(var(--v-theme-surface));
}

.content-card {
  height: 100vh;
  overflow-y: auto;
  border-radius: 0;
  padding: 24px;
  background-color: transparent;
}
</style>
