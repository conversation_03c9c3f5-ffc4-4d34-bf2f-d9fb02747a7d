"use strict";

const { logger } = require("ee-core/log");
const { getMainWindow } = require("ee-core/electron");
const { URL } = require("url");
const { exec } = require("child_process");
const iconv = require("iconv-lite");
const globalStateManager = require("../service/globalStateManager");

class NetworkService {
  constructor() {
    this.pingInterval = null;
    this.pingResults = {};
    this.isMonitoring = false;
    this.monitoringUrls = [];
  }

  /**
   * 开始网络监控
   * @param {Number} interval 监控间隔，单位毫秒
   */
  async startMonitoring(interval = 5000) {
    if (this.isMonitoring) {
      this.stopMonitoring();
    }

    // 获取pad中的URL
    const padParams = await globalStateManager.get("pad");

    if (!padParams) {
      throw new Error("Failed to get pad parameters");
    }

    const { androidUrl, serverUrl, rosUrl } = padParams;

    // 构建监控URL列表
    this.monitoringUrls = [
      { name: "android", url: androidUrl },
      { name: "server", url: serverUrl },
      { name: "ros", url: rosUrl },
    ];

    this.isMonitoring = true;

    // 立即执行一次检测
    this.checkConnectivity();

    // 设置定时检测
    this.pingInterval = setInterval(() => {
      this.checkConnectivity();
    }, interval);

    logger.info(`[networkService] Started monitoring ${this.monitoringUrls.length} URLs with interval ${interval}ms`);
    return true;
  }

  /**
   * 停止网络监控
   */
  stopMonitoring() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
    this.isMonitoring = false;
    logger.info("[networkService] Stopped monitoring");
    return true;
  }

  /**
   * 检测URL连通性
   */
  async checkConnectivity() {
    const results = {};
    const timestamp = new Date().toISOString();

    for (const item of this.monitoringUrls) {
      const { name, url } = item;
      const { isConnected, latency } = await this.pingUrl(url);

      results[name] = {
        url,
        isConnected,
        latency,
        timestamp,
      };
    }

    this.pingResults = results;
    this.sendResultsToRenderer(results, timestamp);

    return results;
  }

  /**
   * Ping URL检测连通性
   * @param {String} url 要检测的URL
   * @returns {Promise<Boolean>} 是否连通
   */
  pingUrl(url) {
    return new Promise((resolve) => {
      try {
        // 提取主机名
        let hostname;
        try {
          // 尝试解析URL
          if (url.includes("://")) {
            // 如果是完整URL，解析出主机名
            const parsedUrl = new URL(url);
            hostname = parsedUrl.hostname;
          } else if (url.includes(":")) {
            // 可能是带端口的主机名，如 ***********:8080
            hostname = url.split(":")[0];
          } else {
            // 直接是主机名或IP
            hostname = url;
          }
        } catch (e) {
          logger.error(`[networkService] Invalid URL: ${url}`, e);
          resolve({ isConnected: false, latency: -1 });
          return;
        }

        // 构建ping命令
        // Windows使用-n参数指定发送次数，Linux/Mac使用-c
        const isWindows = process.platform === "win32";
        const count = isWindows ? "-n 1" : "-c 1";
        const timeout = isWindows ? "-w 3000" : "-W 3";
        const command = `ping ${count} ${timeout} ${hostname}`;

        // 执行ping命令
        exec(command, { timeout: 5000, encoding: "buffer" }, (error, stdout, stderr) => {
          if (error) {
            logger.error(`[networkService] Error pinging ${url}:`, error.message);
            resolve({ isConnected: false, latency: -1 });
            return;
          }

          // 解析ping结果
          const output = isWindows ? iconv.decode(Buffer.from(stdout), "cp936") : stdout.toString();

          // 检查是否有成功的回应
          const isConnected = isWindows
            ? output.includes("Reply from") || output.includes("来自")
            : output.includes(" bytes from ");

          // 尝试提取延迟信息（仅用于日志记录，实际延迟由调用方计算）
          let avgLatency = -1;
          try {
            if (isWindows) {
              // Windows格式: Average = 10ms
              const patterns = [
                /Average = (\d+)ms/, // 英文版
                /平均 = (\d+)ms/, // 中文版
                /平均值 = (\d+)毫秒/, // 其他中文版本
                /最小值 = \d+ms，最大值 = \d+ms，平均 = (\d+)ms/, // 某些 Windows 版本
              ];

              for (const pattern of patterns) {
                const match = output.match(pattern);
                if (match) {
                  avgLatency = parseInt(match[1]);
                  break;
                }
              }
            } else {
              // Linux/Mac格式: min/avg/max/mdev = 0.123/0.456/0.789/0.012 ms
              const match = output.match(/min\/avg\/max\/.+?\s+=\s+[\d\.]+\/([\d\.]+)\/.+?\s+ms/);
              if (match) avgLatency = parseFloat(match[1]);
            }
          } catch (e) {
            logger.warn(`[networkService] Could not parse latency: ${e.message}`);
          }

          if (avgLatency >= 50) {
            logger.error(
              `[networkService] Ping result ip ${hostname} connected=${isConnected}, avg latency=${avgLatency}ms`
            );
          }

          resolve({ isConnected, latency: avgLatency });
        });
      } catch (error) {
        logger.error(`[networkService] Unexpected error pinging ${url}:`, error);
        resolve({ isConnected: false, latency: -1 });
      }
    });
  }

  /**
   * 发送结果到渲染进程进行存储
   * @param {Object} results 检测结果
   * @param {String} timestamp 时间戳
   */
  sendResultsToRenderer(results, timestamp) {
    try {
      const mainWindow = getMainWindow();
      if (!mainWindow.isDestroyed()) {
        // 发送数据到渲染进程，让渲染进程处理存储逻辑
        mainWindow.webContents.send("network-status-update", { timestamp, results });
      }
    } catch (error) {
      logger.error("[networkService] Error sending results to renderer:", error);
    }
  }

  /**
   * 获取当前监控状态
   * @returns {Object} 监控状态
   */
  getStatus() {
    return {
      isMonitoring: this.isMonitoring,
      monitoringUrls: this.monitoringUrls,
      latestResults: this.pingResults,
    };
  }
}

module.exports = new NetworkService();
