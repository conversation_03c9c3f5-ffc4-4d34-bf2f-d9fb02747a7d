"use strict";

const storage = require("electron-json-storage");
const { ipcMain } = require("electron");
const { logger } = require("ee-core/log");
const { app } = require("electron");

class GlobalStateManager {
  static instance = null;

  static getInstance() {
    if (!GlobalStateManager.instance) {
      GlobalStateManager.instance = new GlobalStateManager();
    }
    return GlobalStateManager.instance;
  }

  constructor() {
    if (GlobalStateManager.instance) {
      return GlobalStateManager.instance;
    }
    this.storageKey = "global_state";
    this.defaultData = {
      vehicle_id: "",
      vehicle_name: "",
      vehicle_type: 1,
      pad: {
        status: 8, // 0: 初始化 8: 初始化 16: 等待 32: 等待 48: 登录 64: 远控 80: 锁定 88: 失效保护
        androidUrl: "**************",
        androidWSPort: "20009",
        serverUrl: "*************",
        rosUrl: "***************",
        btnList: [],
      },
      usb: {
        devicePath: "", // USB设备路径，如 "/dev/ttyUSB0" 或 "COM3"
        enabled: true, // 是否启用USB连接
        autoDetect: true, // 是否自动检测USB设备
      },
      android: {
        platformParams: {
          parameterPlatform: "",
          parameterPlatformPort: "",
        },
      },
      localAndroid: {},
      handleConfig: {},
      intelligent: {},
      online: false,
      locked_opc: "",
      is_lock: 1,
    };

    // 设置存储路径为应用用户数据目录
    storage.setDataPath(app.getPath("userData"));

    this._initIPC();
  }

  _initIPC() {
    ipcMain.handle("global-state:get", async (event, key) => {
      const value = await this.get(key);
      return value;
    });

    ipcMain.handle("global-state:getAll", async (event) => {
      return this.data;
    });

    ipcMain.handle("global-state:set", (event, key, value) => {
      return this.set(key, value);
    });

    ipcMain.handle("global-state:delete", (event, key) => {
      return this.delete(key);
    });

    ipcMain.handle("global-state:clear", () => {
      return this.clear();
    });

    ipcMain.handle("global-state:setAll", (event, newData, mergeWithDefault = true) => {
      return this.setAll(newData, mergeWithDefault);
    });
  }

  async initialize() {
    try {
      const data = await this._loadFromStorage();
      this.data = { ...this.defaultData, ...data };
      logger.info("[GlobalState] Global state initialization completed");
    } catch (error) {
      logger.error("[GlobalState] Initialization failed:", error);
      this.data = { ...this.defaultData };
    }
  }

  async _loadFromStorage() {
    return new Promise((resolve, reject) => {
      storage.get(this.storageKey, (error, data) => {
        if (error) return reject(error);
        resolve(data);
      });
    });
  }

  async _saveToStorage() {
    return new Promise((resolve, reject) => {
      storage.set(this.storageKey, this.data, (error) => {
        if (error) return reject(error);
        resolve();
      });
    });
  }

  /**
   * 获取全局状态中指定键的值
   * @param {string} key - 点分隔的键路径，例如 'preferences.theme.color'
   * @returns {any} 返回找到的值，如果路径无效则返回 undefined
   *
   * 实现说明：
   * 1. 使用 split('.') 将键字符串分割成路径数组
   * 2. 使用 reduce 方法遍历路径数组
   * 3. 通过可选链操作符 (?.) 安全地访问嵌套对象
   * 4. 如果中间路径不存在，将返回 undefined
   */
  async get(key) {
    return key.split(".").reduce((o, i) => o?.[i], this.data);
  }

  async set(key, value) {
    const keys = key.split(".");
    const lastKey = keys.pop();
    let nested = this.data;

    for (const k of keys) {
      nested = nested[k] = nested[k] || {};
    }
    nested[lastKey] = value;

    await this._saveToStorage();
    return true;
  }

  async delete(key) {
    const keys = key.split(".");
    const lastKey = keys.pop();
    let nested = this.data;

    for (const k of keys) {
      if (!nested[k]) return false;
      nested = nested[k];
    }
    delete nested[lastKey];

    await this._saveToStorage();
    return true;
  }

  async clear() {
    this.data = JSON.parse(JSON.stringify(this.defaultData));
    await this._saveToStorage();
    return true;
  }

  /**
   * 修改所有内容
   * @param {Object} newData - 新的数据对象
   * @param {boolean} [mergeWithDefault=true] - 是否与默认数据合并
   * @returns {boolean} 操作是否成功
   *
   * 实现说明：
   * 1. 如果 mergeWithDefault 为 true，将保留默认数据的结构，只更新提供的字段
   * 2. 如果 mergeWithDefault 为 false，将完全替换数据（但保留默认数据中未提供的字段）
   * 3. 更新后的数据会立即保存到存储中
   */
  async setAll(newData, mergeWithDefault = true) {
    try {
      if (mergeWithDefault) {
        // 深度合并对象，保留默认数据的结构
        this.data = this._deepMerge(JSON.parse(JSON.stringify(this.data)), newData);
      } else {
        // 直接替换，但保留默认数据中未提供的字段
        this.data = { ...JSON.parse(JSON.stringify(this.data)), ...newData };
      }

      await this._saveToStorage();
      logger.info("[GlobalState] All data has been updated");
      return true;
    } catch (error) {
      logger.error("[GlobalState] Failed to update all data:", error);
      return false;
    }
  }

  /**
   * 深度合并两个对象
   * @private
   * @param {Object} target - 目标对象
   * @param {Object} source - 源对象
   * @returns {Object} 合并后的对象
   */
  _deepMerge(target, source) {
    if (!source) return target;

    const output = { ...target };

    if (this._isObject(target) && this._isObject(source)) {
      Object.keys(source).forEach((key) => {
        if (this._isObject(source[key])) {
          if (!(key in target)) {
            Object.assign(output, { [key]: source[key] });
          } else {
            output[key] = this._deepMerge(target[key], source[key]);
          }
        } else {
          Object.assign(output, { [key]: source[key] });
        }
      });
    }

    return output;
  }

  /**
   * 检查值是否为对象
   * @private
   * @param {any} item - 要检查的值
   * @returns {boolean} 是否为对象
   */
  _isObject(item) {
    return item && typeof item === "object" && !Array.isArray(item);
  }

  async cleanup() {
    try {
      await this._saveToStorage();
      logger.info("[GlobalState] Global state has been persisted");
    } catch (error) {
      logger.error("[GlobalState] Storage failed:", error);
    }
  }
}

module.exports = GlobalStateManager.getInstance();
