"use strict";

const { logger } = require("ee-core/log");
const MemoryMonitorService = require("../service/memoryMonitorService");

/**
 * 内存监控控制器
 * 用于从渲染进程访问内存监控服务
 */
class MemoryController {
  constructor() {
    this.memoryMonitorService = MemoryMonitorService;
  }

  /**
   * 所有方法接收两个参数
   * @param args 前端传的参数
   * @param event - ipc通信时才有值
   */

  /**
   * 开始内存监控
   * @param {Object} args 监控选项
   * @param {Number} args.interval 监控间隔（毫秒）
   * @param {Number} args.maxDataPoints 最大数据点数量
   * @param {Number} args.alertThreshold 警告阈值（MB）
   * @param {Boolean} args.autoHeapDump 是否自动生成堆快照
   * @param {Number} args.heapDumpInterval 堆快照间隔（毫秒）
   * @returns {Boolean} 是否成功启动监控
   */
  async startMonitoring(args = {}) {
    try {
      const result = this.memoryMonitorService.startMonitoring(args);
      return result;
    } catch (error) {
      logger.error("[memoryController] Failed to start monitoring:", error);
      throw error;
    }
  }

  /**
   * 停止内存监控
   * @returns {Boolean} 是否成功停止监控
   */
  async stopMonitoring() {
    try {
      const result = this.memoryMonitorService.stopMonitoring();
      return result;
    } catch (error) {
      logger.error("[memoryController] Failed to stop monitoring:", error);
      throw error;
    }
  }

  /**
   * 获取内存监控状态
   * @returns {Object} 监控状态
   */
  async getStatus() {
    try {
      return {
        isMonitoring: this.memoryMonitorService.isMonitoring,
        interval: this.memoryMonitorService.monitoringInterval,
        dataPoints: this.memoryMonitorService.memoryData.length,
        maxDataPoints: this.memoryMonitorService.maxDataPoints,
        alertThreshold: this.memoryMonitorService.alertThreshold,
        autoHeapDump: this.memoryMonitorService.autoHeapDump,
      };
    } catch (error) {
      logger.error("[memoryController] Failed to get status:", error);
      throw error;
    }
  }

  /**
   * 获取内存监控数据
   * @returns {Array} 内存监控数据
   */
  async getData() {
    try {
      return this.memoryMonitorService.memoryData;
    } catch (error) {
      logger.error("[memoryController] Failed to get data:", error);
      throw error;
    }
  }

  /**
   * 生成堆快照
   * @returns {String} 堆快照文件路径
   */
  async generateHeapSnapshot() {
    try {
      const result = await this.memoryMonitorService.generateHeapSnapshot();
      return result;
    } catch (error) {
      logger.error("[memoryController] Failed to generate heap snapshot:", error);
      throw error;
    }
  }

  /**
   * 清除内存监控数据
   * @returns {Boolean} 是否成功清除数据
   */
  async clearData() {
    try {
      this.memoryMonitorService.memoryData = [];
      return true;
    } catch (error) {
      logger.error("[memoryController] Failed to clear data:", error);
      throw error;
    }
  }

  /**
   * 获取当前内存使用情况
   * @returns {Object} 当前内存使用情况
   */
  async getCurrentMemoryUsage() {
    try {
      const mainProcessMemory = process.memoryUsage();
      return {
        rss: Math.round(mainProcessMemory.rss / 1024 / 1024), // 常驻集大小 (MB)
        heapTotal: Math.round(mainProcessMemory.heapTotal / 1024 / 1024), // 总堆内存 (MB)
        heapUsed: Math.round(mainProcessMemory.heapUsed / 1024 / 1024), // 已用堆内存 (MB)
        external: Math.round(mainProcessMemory.external / 1024 / 1024), // 外部内存 (MB)
        arrayBuffers: Math.round((mainProcessMemory.arrayBuffers || 0) / 1024 / 1024), // ArrayBuffers内存 (MB)
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      logger.error("[memoryController] Failed to get current memory usage:", error);
      throw error;
    }
  }
}

module.exports = MemoryController;
