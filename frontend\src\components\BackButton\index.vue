<template>
  <v-btn
    class="back-button"
    icon
    @touchstart="startDrag"
    @touchmove="onDrag"
    @touchend="stopDrag"
    @touchcancel="stopDrag"
    @click="handleBack"

    :style="{
      left: `${position.x}px`,
      top: `${position.y}px`,
      cursor: isDragging ? 'move' : 'pointer'
    }"
  >
    <v-icon>mdi-arrow-left</v-icon>
  </v-btn>
</template>

<script setup>
import { ref } from "vue";
import { useRouter } from "vue-router";

const router = useRouter();
const position = ref({ x: window.innerWidth - 100, y: window.innerHeight - 100 });
const isDragging = ref(false);
const dragOffset = ref({ x: 0, y: 0 });

const handleBack = () => {
  if (!isDragging.value) {
    router.back();
  }
};

const startDrag = (event) => {
  isDragging.value = true;
  dragOffset.value = {
    x: event.touches[0].clientX - position.value.x,
    y: event.touches[0].clientY - position.value.y,
  };
};

const onDrag = (event) => {
  if (!isDragging.value) return;

  const newX = event.touches[0].clientX - dragOffset.value.x;
  const newY = event.touches[0].clientY - dragOffset.value.y;

  position.value = {
    x: Math.min(Math.max(0, newX), window.innerWidth - 56),
    y: Math.min(Math.max(0, newY), window.innerHeight - 56),
  };
};

const stopDrag = (event) => {
  event.stopPropagation();
  isDragging.value = false;
};
</script>

<style scoped>
.back-button {
  position: fixed;
  z-index: 1000;
}
</style>
