# 拓疆者操作台辅助屏项目 - LLM 指南

## 项目概述

这是一个基于Electron-egg框架开发的跨平台桌面应用，主要功能是作为"拓疆者操作台"的辅助显示屏。该应用运行在工程机械操作台的辅助平板设备上，提供操作界面和数据显示功能。

## 技术栈

- **前端**: Vue 3 + Vuetify + Vite
- **后端**: Electron
- **通信**: MQTT, WebSocket
- **构建工具**: electron-builder
- **支持平台**: Windows, Linux (ARM64), macOS

## 项目结构

```
bux-electron-aux-screen/
├── electron/           # Electron主进程代码
│   ├── config/         # 配置文件
│   ├── controller/     # 控制器
│   ├── service/        # 服务层
│   └── utils/          # 工具函数
├── frontend/           # Vue3前端代码
│   ├── src/            # 源代码
│   │   ├── views/      # 页面组件
│   │   ├── components/ # 通用组件
│   │   ├── router/     # 路由配置
│   │   ├── store/      # 状态管理
│   │   └── i18n/       # 国际化
│   └── public/         # 静态资源
├── cmd/                # 构建配置
└── build/              # 构建资源
```

## 主要功能模块

1. **欢迎界面**: 应用启动入口，包含语言选择
2. **车辆选择**: 选择要操作的工程车辆
3. **操作界面**: 主要操作和监控界面
4. **调试工具**: 包含MQTT调试、日志查看等功能
5. **网络监测**: 监测与各系统的网络连接状态
6. **参数配置**: 系统参数配置界面

## 关键技术点

- **多进程架构**: Electron主进程与渲染进程分离
- **跨平台兼容**: 支持Windows、Linux(ARM64)和macOS
- **实时通信**: 使用MQTT和WebSocket进行实时数据传输
- **国际化**: 支持中文、英文和日语
- **全屏显示**: 在平板设备上全屏运行，无滚动条
- **主题切换**: 支持明暗两种主题

## 相关文档链接

- [README.md](./README.md): 项目概述和使用说明
- [test-plan.md](./test-plan.md): 测试计划文档

## API和集成点

- **MQTT**: 用于与车辆控制系统通信
- **WebSocket**: 用于与Android系统通信
- **IPC**: Electron主进程与渲染进程间通信

## 部署信息

应用支持以下部署方式:
- Windows: .exe安装包
- Linux: .deb包，特别支持ARM64架构
- macOS: .dmg安装包

## 开发指南

1. 开发环境设置:
   ```
   npm install
   npm run dev
   ```

2. 构建应用:
   ```
   npm run build
   npm run build-l-arm64  # 构建Linux ARM64版本
   ```

3. 调试工具:
   - 应用内置Debug模块，包含MQTT调试、日志查看等功能

## 注意事项

- 应用设计为在平板设备上运行，UI针对触摸操作优化
- 屏幕分辨率固定为1280x800，不支持响应式布局
- 不支持滚动条，所有内容需在一屏内显示
- 使用Vuetify组件库，支持明暗两种主题
