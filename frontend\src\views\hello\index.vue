<template>
  <div>
    <Space ref="spaceRef" />
    <div class="content" v-if="status === 'power_on'">
      <img class="w-[490px]" src="@/assets/image/logo_en_black.png" alt="" srcset="" />
    </div>
    <div class="content" v-else-if="status === 'lang_select'">
      <v-row class="fill-height" justify="center">
        <v-col v-for="lang in languages" :key="lang.value" cols="4" class="pa-2 flex justify-center">
          <v-card
            class="lang-card"
            :class="{ selected: selectedLang === lang.value }"
            @click="selectLanguage(lang.value)"
            elevation="2"
            hover
          >
            <v-card-title class="text-center d-flex flex-column align-center justify-center gap-2">
              <v-icon size="48">{{ lang.icon }}</v-icon>
              <span class="pt-5 text-2xl">{{ lang.label }}</span>
            </v-card-title>
          </v-card>
        </v-col>
      </v-row>
      <v-row class="mt-14 button" justify="center">
        <v-switch
          v-model="notShowAgain"
          label="下次进入跳过语言选择"
          hide-details
          @change="handleLanguageShowChange"
        ></v-switch>
      </v-row>
      <v-row class="mt-14 button">
        <!--           @mousedown="handleMouseStart"
          @mouseup="handleMouseEnd"
          @touchstart="handleMouseStart"
          @touchend="handleMouseEnd" -->
        <div class="login btn" :class="{ active: isActive }" @click="confirmLanguage">
          <span>确认选择</span>
        </div>
      </v-row>
    </div>
    <div class="content" v-else-if="status === 'vehicle_select'">
      <v-row class="fill-height flex-nowrap overflow-x-auto" style="width: 84vw">
        <v-col v-for="veh in vehicles" :key="veh.vehicle_id" class="pa-2 px-4 flex justify-center">
          <v-card
            class="lang-card !rounded-[12px]"
            :class="{ selected: selectedVeh === veh.vehicle_id }"
            @click="selectVehicle(veh.vehicle_id)"
            elevation="2"
            hover
          >
            <v-card-title class="text-center d-flex flex-column align-center justify-center gap-2">
              <v-icon size="58">{{ veh.icon }}</v-icon>
              <span class="pt-7 text-2xl">{{ veh.vehicle_name }}</span>
            </v-card-title>
          </v-card>
        </v-col>
      </v-row>
      <v-row class="mt-14 button">
        <div class="login btn" style="border: none; cursor: default">请旋转钥匙登录系统</div>
      </v-row>
      <v-row class="mt-14 button" justify="center">
        <v-switch
          v-model="asynchronous"
          label="异步监测"
          active-color="green"
          inactive-color="red"
          hide-details
          @change="handleAsyncChange"
        ></v-switch>
      </v-row>
    </div>
    <div class="content" v-else-if="status === 'loading'">
      <div>
        <v-progress-circular color="#bbb" indeterminate size="98" width="6" />
        <div class="flex items-center justify-center mt-6">
          <span class="text-xl text-gray-300">加载中...</span>
        </div>
      </div>
    </div>
    <div class="content" v-else>
      <div
        class="login btn"
        :class="{ active: isActive }"
        @mousedown="handleMouseStart"
        @mouseup="handleMouseEnd"
        @touchstart="handleMouseStart"
        @touchend="handleMouseEnd"
      >
        <span>进入系统</span>
      </div>
    </div>
    <BackButton />
  </div>
</template>
<script setup>
import { onMounted, onUnmounted, ref } from "vue";
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import BackButton from "@/components/BackButton/index.vue";
import { ipc } from "@/utils/ipcRenderer";
import { sendSync2Android } from "@/utils/androidMessage";
import Space from "./space.vue";
import { iconEnum } from "@/utils/constants";
import { registerWsHandler, unregisterWsHandler } from "@/utils/ipcMessageHandler";

const router = useRouter();
const { locale } = useI18n();
const spaceRef = ref(null);

const languages = [
  { value: "zh", label: "中文", icon: "mdi-alpha-c-circle" },
  { value: "en", label: "English", icon: "mdi-alpha-e-circle" },
  { value: "ja", label: "日本語", icon: "mdi-alpha-j-circle" },
];
const selectedLang = ref(locale.value);
const notShowAgain = ref(false);

const selectLanguage = async (lang) => {
  selectedLang.value = lang;
  locale.value = lang;
  await sendSync2Android(1, {
    selectLanguage: selectedLang.value, // 当前选中的语言
    isOk: false, // 是否确认选择语言
  });
};

const handleLanguageShowChange = () => {
  sendSync2Android(1, {
    selectLanguage: selectedLang.value, // 当前选中的语言
    isOk: false, // 是否确认选择语言
    notShowAgain: notShowAgain.value,
  });
}

const confirmLanguage = async () => {
  await sendSync2Android(1, {
    selectLanguage: selectedLang.value, // 当前选中的语言
    isOk: true, // 是否确认选择语言
  });
};

const vehicles = ref([]);
const asynchronous = ref(false);

// default: "power_on",
// default: "lang_select",
// default: "vehicle_select",
// default: "loading",
const status = ref("loading");
const selectedVeh = ref("car");

const selectVehicle = async (veh) => {
  selectedVeh.value = veh;
  send2AndroidData({ select_vehicle_id: selectedVeh.value, asynchronous: asynchronous.value });
};

const handleAsyncChange = () => {
  send2AndroidData({ select_vehicle_id: selectedVeh.value, asynchronous: asynchronous.value });
};

const isActive = ref(false);

const send2AndroidData = async (data) => {
  await sendSync2Android(2, data);
};

// 处理WebSocket消息
const handleWsMessage = (event, msg) => {
  const { type, page, payload } = msg.jsonData;
  if (type === "sync") {
    if (page === 0) {
      status.value = "loading";
    } else if (page === 1) {
      status.value = "lang_select";
      selectedLang.value = payload.selectLanguage;
      notShowAgain.value = payload.notShowAgain;

    } else if (page === 2) {
      vehicles.value = payload.vehicle_list;
      if (vehicles.value.length > 0) {
        status.value = "vehicle_select";
        const flag = vehicles.value.findIndex((item) => payload.select_vehicle_id === item.vehicle_id);
        if (flag !== -1) selectedVeh.value = payload.select_vehicle_id;
        asynchronous.value = payload.asynchronous;
      } else {
        status.value = "loading";
      }
      payload.vehicle_list.map((item) => {
        item.icon = iconEnum[item.vehicle_type.slice] || "mdi-excavator";
      });
    } else if (page === 3) {
      // 进入本地配置模式，有个弹窗
    }
  }
};

let handlerId = null;

onMounted(() => {
  // 注册WebSocket消息处理函数
  handlerId = registerWsHandler("*", handleWsMessage);
});

// 组件卸载时注销消息处理函数
onUnmounted(() => {
  if (handlerId) {
    unregisterWsHandler(handlerId);
  }
});

const handleMouseStart = (e) => {
  isActive.value = true;
  spaceRef.value.handleMouseStart();
};

const handleMouseEnd = (e) => {
  isActive.value = false;
  spaceRef.value.handleMouseEnd();
};
</script>
<style scoped lang="scss">
.lang-card {
  width: 180px;
  height: 240px;
  padding-top: 50px;
  border: 2px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);

  &.selected {
    border-color: var(--v-theme-surface);
    box-shadow: 0 0 15px rgba(33, 150, 243, 0.3);
  }

  &:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.1);
  }
}

canvas {
  height: 100vh;
  width: 100vw;
  background-color: hsl(256, 100%, 5%);
}

.content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.button {
  .login {
    color: #ddd;
    margin: 0 auto;
    cursor: pointer;
    user-select: none;
  }
}
.close {
  position: absolute;
  bottom: 0%;
  right: 0%;
  width: 100px;
  height: 28px;
  color: #ddd;
  margin: 0 auto;
  line-height: 28px;
  font-size: 12px;
  user-select: none;
}

.btn {
  position: relative;
  border: 1px solid #fff;
  border-radius: 8px;
  background: transparent;
  font-size: 20px;
  font-weight: bold;
  text-align: center;
  text-decoration: none;
  text-transform: uppercase;
  transition-delay: 0.8s;
  overflow: hidden;
  transition: 0.5s;
  padding: 10px 48px;
  transition:
    border 0.5s ease-out,
    box-shadow 0.5s ease-out;
}
.btn:before,
.btn:after {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  margin: auto;
  content: "";
  border-radius: 50%;
  display: block;
  width: 300px;
  height: 300px;
  left: -98px;
  text-align: center;
  transition: box-shadow 0.5s ease-out;
  transition-delay: 0.75s;
}
.btn:after {
  transition-delay: 0.25s;
}
.btn span {
  position: relative;
  z-index: 1;
  pointer-events: none;
}
.btn.active {
  color: #ffffff;
  transform: scale(0.95);
  transition: 0.2s ease;
}
.btn.active::before {
  transition-delay: 0.2s;
  box-shadow: inset 0 0 0 200px #747373;
}
.btn.active::after {
  color: #ffffff;
  border: none;
  box-shadow: inset 0 0 0 200px #020202;
  transition-delay: 0.5s;
}
</style>
