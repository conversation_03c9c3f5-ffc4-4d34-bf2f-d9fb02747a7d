const obj = {
  haveAttitudePerception: true, // 是否有姿态感知
  have_person_flag: true, // 是否有有人标志
  have_robot_anti_collision: true, // 是否有机器人防碰撞
  have_auto_reset: true, // 是否有自动复位
  have_bucket_landing_point: true, // 是否有斗着陆点
  have_bucket_tooth: true, // 是否有斗齿
  have_ground_level: true, // 是否有地面标高
  have_dust_seen_through: true, // 是否有透尘功能
  have_remote_power_on: true, // 是否有远程上电
  have_side_profile_radar: true, // 是否有侧面轮廓雷达

  // 姿态感知
  attitude_perception: {
    color: "00ff3a", // 颜色
    form: 0, // 形状
    switch: 1, // 是否开启
    switch2: 1, // 声音 是否开启
    transparency: 100, // 透明度
  },
  // 有人标志配置
  has_person_flag: {
    color: "fefe3e", // 颜色
    form: 0, // 形状
    switch: 1, // 是否开启
    transparency: 0, // 透明度
  },
  // 防碰撞机器人配置
  aif_robot_anti_collision: {
    color: "00fffe", // 颜色
    form: 1, // 形状
    transparency: 100, // 透明度
    switch: 1, // 是否开启
    switch2: 0, // 声音开关
    switch3: 0, // 防碰撞刹车开关
  },
  // 自动复位配置
  auto_reset: {
    color: "00ff3a", // 颜色
    form: 0, // 形状
    switch: 1, // 是否开启
    transparency: 100, // 透明度
  },
  // 斗着陆点配置
  bucket_landing_point: {
    color: "fefe3e", // 颜色
    form: 0, // 形状
    switch: 1, // 是否开启
    transparency: 57, // 透明度
  },
  // 斗齿配置
  bucket_tooth: {
    color: "fefe3e", // 颜色
    form: 0, // 形状
    switch: 1, // 是否开启
    transparency: 0, // 透明度
  },
  // 地面标高配置
  ground_level: {
    color: "fefe3e", // 颜色
    form: 0, // 形状
    switch: 1, // 是否开启
    transparency: 0, // 透明度
  },
  // 粉尘透视
  aif_dust_seen_through: {
    color: "00fffe",
    form: 1,
    switch: 0,
    transparency: 78,
  },
  // 远程上电
  remote_power_on: {
    switch: 1, // 上电开关
    switch2: 2, // 下电开关
  },
  // 侧面轮廓雷达配置
  side_profile_radar: {
    color: "00ff3a", // 颜色
    form: 0, // 形状
    switch: 1, // 是否开启
    transparency: 100, // 透明度
  },
  // 倾斜报警平台配置
  tiltAlarmPlatform: [
    {
      isUse: true, // 是否启用
      maxXValue: 45, // X轴最大值
      maxYValue: 45, // Y轴最大值
      minXValue: 5, // X轴最小值
      minYValue: 5, // Y轴最小值
      name: "A", // 平台名称
      warmXValue: 40, // X轴预警值
      warmYValue: 26, // Y轴预警值
    },
    {
      isUse: false, // 是否启用
      maxXValue: 45, // X轴最大值
      maxYValue: 45, // Y轴最大值
      minXValue: 5, // X轴最小值
      minYValue: 5, // Y轴最小值
      name: "B", // 平台名称
      warmXValue: 14, // X轴预警值
      warmYValue: 14, // Y轴预警值
    },
  ],
  // 雷达相关参数
  radarValues: {
    isAntiCollisionKillStopSwitch: false, // 防碰撞急停开关
    loaderLength: 8.53, // 装载机长度
    roundToRound: 2.15, // 轮到轮距离
    maxWidth: 2.85, // 最大宽度
    radarGreenValue: 8.8, // 雷达绿色阈值
    radarRedValue: 1.6, // 雷达红色阈值
    radarYellowValue: 4.1, // 雷达黄色阈值
    valueLinkPointToRearDistance: 2.36, // 链接点到后部距离
  },
};

const defaultData = {
  vehicle_id: "66f135aa8ea96a77201f53e9",
  vehicle_name: "测试机器-wlf",
  vehicle_type: 1,
  pad: {
    status: 8, // 0: 初始化 8: 初始化 16: 等待 32: 等待 48: 登录 64: 远控 80: 锁定 88: 失效保护
    androidUrl: "192.168.70.234",
    androidWSPort: "20009",
    serverUrl: "192.168.70.43",
    rosUrl: "192.168.224.203",
    btnList: [
      {
        index: "0",
        label: "探照灯",
        name: "light",
        mode: "toggle", // toggle 开关 press 按压
        type: 1, // 1: 车端按钮 2: 智能化设置 3: 布局按钮
        value: true, // 值为true或false
        icon: "icon-1", // 图标
      },
      {
        index: "22",
        label: "布局切换",
        name: "layout",
        mode: "toggle", // toggle 开关 press 按压
        type: 4, // 1: 车端按钮 2: 智能化设置 3: 布局按钮
        value: true, // 值为true或false
        icon: "icon-1", // 图标
      },
      {
        index: "23",
        label: "BDI开关",
        name: "bdi",
        mode: "toggle", // toggle 开关 press 按压
        type: 2, // 1: 车端按钮 2: 智能化设置 3: 布局按钮
        value: true, // 值为true或false
        icon: "icon-1", // 图标
      },
    ],
  },
  android: {
    projectName: "test_waji_big_01",
    payEnccn: "F1244C3E79B73BC51E6A605C1E3DDE5A0D983648A94371BEE4BF85266F524E2F",
    handlerType: 0,
    excavatorType: 4,
    excavatorSubType: 1,
    mqttParams: {
      mqttClientID: "vehicle_fangshan_001",
      mqttHost: "mqtt://mqtt.apps.builderx.com:1883",
      mqttName: "builderx",
      mqttPassword: "builderx",
      mqttKeepLive: "test_zjFMJxkJ/keep_live",
      mqttToAndroid: "test_zjFMJxkJ/to_android",
      mqttToExcavator: "test_zjFMJxkJ/to_excavator",
      mqttToTripartite: "",
      mqttToAndroidTripartite: "",
      mqttToAndroidTripartiteEx001: "",
    },
    playParams: {
      playUrlVideo: "rtsp://api.builderx.com:28554/live/pro/test_zjFMJxkJ/stream0",
      // playUrlVideo: "rtsp://api.builderx.com/live/pro/test_666/stream0",
      playUrlAudio: "rtsp://api.builderx.com/live/pro/test_666/voice",
      playUrlShow: "",
      videoType: 0,
    },
    powerOnParams: {
      niRenDTO: true,
      powerOnCommand: "",
      powerOffCommand: "",
      allowPowerOff: true,
      allowPowerOn: false,
      mqttToAndroidPowerON: "/test_666/power_on_test",
      androidToMqttPowerON: "/test_666/power_status",
      powerQuery: false,
    },
    selfTestParams: {
      controlMecIp: "************",
    },
    platformParams: {
      parameterPlatform: "",
      parameterPlatformPort: "",
    },
    heartManage: {},
    networkStatus: {
      delayMode: false,
      pingPort: 12345,
      ntpUrl: "",
      excellent: 100,
      goodPeople: 500,
      packetLossRate: 2.0,
      pingWarnValue: 50,
    },
    parrotagent: {},
    switchExtendedFunctionParams: {
      selfStart: false,
      haveScreenRecording: false,
      haveParrotAgent: false,
      hadRadarHalt: false,
      havePilotControl: false,
      leftHandleReversal: false, // 是否使用左手柄反转
      sendExtendParam: false,
      usesTheLeftHandle: false,
      simulatePedal: false,
      mixedFlowSetting: true,
      doubleDipAngle: true,
      needSend303: true,
    },
    showViewParams: {
      showPowerOnSelfTest: true,
      showRockerView: true, // 是否显示摇杆 手柄指示器
      showBalancerView: true,
      showRemoteState: true,
      hidePowerOnState: false,
      showEngineTime: false,
      showLeftMainPumpPressure: false,
      showRightMainPumpPressure: false,
      showReduceThrottle: false,
      noIgnitionMinSpeedValue: 609,
      showOilLevel: true,
      showEngineSpeed: true,
      showWaterTemp: true,
      showBatteryVoltage: true,
      showOilPressure: true,
      showHydraulicOilTemp: true,
      showTurnSignal: false,
      showAuto: true,
      showOilConsumption: true,
      showIdle: false,
      showTravelSpeed: false,
      doubleFlashTurnLamp: false,
    },
  },
  handleConfig: {
    handleSettingList: [
      {
        isPublic: false, // 是否公开
        isUse: true, // 是否使用
        step_switch: false, // 是否使用步长
        arm_down: {
          low_limit: 7,
          sensitivity: 1,
          step: 10,
          up_limit: 100,
        },
        arm_up: {
          low_limit: 9,
          sensitivity: 1,
          step: 10,
          up_limit: 100,
        },
        boom_down: {
          low_limit: 7,
          sensitivity: 1,
          step: 10,
          up_limit: 100,
        },
        boom_up: {
          low_limit: 7,
          sensitivity: 1,
          step: 10,
          up_limit: 100,
        },
        bucket_down: {
          low_limit: 7,
          sensitivity: 1,
          step: 10,
          up_limit: 100,
        },
        bucket_up: {
          low_limit: 7,
          sensitivity: 1,
          step: 10,
          up_limit: 100,
        },

        simulate_left_after: {
          low_limit: 7,
          sensitivity: 1,
          step: 10,
          up_limit: 100,
        },
        simulate_left_front: {
          low_limit: 7,
          sensitivity: 1,
          step: 10,
          up_limit: 100,
        },
        simulate_right_after: {
          low_limit: 7,
          sensitivity: 1,
          step: 10,
          up_limit: 100,
        },
        simulate_right_front: {
          low_limit: 7,
          sensitivity: 1,
          step: 10,
          up_limit: 100,
        },
        swing_left: {
          low_limit: 7,
          sensitivity: 1,
          step: 10,
          up_limit: 100,
        },
        swing_right: {
          low_limit: 7,
          sensitivity: 1,
          step: 10,
          up_limit: 100,
        },
        track_left_after: {
          low_limit: 7,
          sensitivity: 1,
          step: 10,
          up_limit: 100,
        },
        track_left_front: {
          low_limit: 7,
          sensitivity: 1,
          step: 10,
          up_limit: 100,
        },
        track_right_after: {
          low_limit: 7,
          sensitivity: 1,
          step: 10,
          up_limit: 100,
        },
        track_right_front: {
          low_limit: 7,
          sensitivity: 1,
          step: 10,
          up_limit: 100,
        },
      },
      {
        isPublic: false,
        isUse: false,
        step_switch: false,
        arm_down: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },
        arm_up: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },
        boom_down: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },
        boom_up: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },
        bucket_down: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },
        bucket_up: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },

        simulate_left_after: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },
        simulate_left_front: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },
        simulate_right_after: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },
        simulate_right_front: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },
        swing_left: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },
        swing_right: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },
        track_left_after: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },
        track_left_front: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },
        track_right_after: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },
        track_right_front: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },
      },
      {
        isPublic: false,
        isUse: false,
        step_switch: false,
        arm_down: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },
        arm_up: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },
        boom_down: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },
        boom_up: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },
        bucket_down: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },
        bucket_up: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },
        simulate_left_after: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },
        simulate_left_front: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },
        simulate_right_after: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },
        simulate_right_front: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },
        swing_left: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },
        swing_right: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },
        track_left_after: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },
        track_left_front: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },
        track_right_after: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },
        track_right_front: {
          low_limit: 0,
          sensitivity: 0,
          step: 10,
          up_limit: 100,
        },
      },
    ],
  },
  intelligent: {
    haveAttitudePerception: true, // 是否有姿态感知
    have_person_flag: true, // 是否有有人标志
    have_robot_anti_collision: true, // 是否有机器人防碰撞
    have_auto_reset: true, // 是否有自动复位
    have_bucket_landing_point: true, // 是否有斗着陆点
    have_bucket_tooth: true, // 是否有斗齿
    have_ground_level: true, // 是否有地面标高
    have_dust_seen_through: true, // 是否有透尘功能
    have_remote_power_on: true, // 是否有远程上电
    have_side_profile_radar: true, // 是否有侧面轮廓雷达

    // 姿态感知
    attitude_perception: {
      color: "00ff3a", // 颜色
      form: 0, // 形状
      switch: 1, // 是否开启
      switch2: 1, // 声音 是否开启
      transparency: 100, // 透明度
    },
    // 有人标志配置
    has_person_flag: {
      color: "fefe3e", // 颜色
      form: 0, // 形状
      switch: 1, // 是否开启
      transparency: 0, // 透明度
    },
    // 防碰撞机器人配置
    aif_robot_anti_collision: {
      color: "00fffe", // 颜色
      form: 1, // 形状
      transparency: 100, // 透明度
      switch: 1, // 是否开启
      switch2: 0, // 声音开关
      switch3: 0, // 防碰撞刹车开关
    },
    // 自动复位配置
    auto_reset: {
      color: "00ff3a", // 颜色
      form: 0, // 形状
      switch: 1, // 是否开启
      transparency: 100, // 透明度
    },
    // 斗着陆点配置
    bucket_landing_point: {
      color: "fefe3e", // 颜色
      form: 0, // 形状
      switch: 1, // 是否开启
      transparency: 57, // 透明度
    },
    // 斗齿配置
    bucket_tooth: {
      color: "fefe3e", // 颜色
      form: 0, // 形状
      switch: 1, // 是否开启
      transparency: 0, // 透明度
    },
    // 地面标高配置
    ground_level: {
      color: "fefe3e", // 颜色
      form: 0, // 形状
      switch: 1, // 是否开启
      transparency: 0, // 透明度
    },
    // 粉尘透视
    aif_dust_seen_through: {
      color: "00fffe",
      form: 1,
      switch: 0,
      transparency: 78,
    },
    // 远程上电
    remote_power_on: {
      switch: 1, // 上电开关
      switch2: 2, // 下电开关
    },
    // 侧面轮廓雷达配置
    side_profile_radar: {
      color: "00ff3a", // 颜色
      form: 0, // 形状
      switch: 1, // 是否开启
      transparency: 100, // 透明度
    },
    // 倾斜报警平台配置
    tiltAlarmPlatform: [
      {
        isUse: true, // 是否启用
        maxXValue: 45, // X轴最大值
        maxYValue: 45, // Y轴最大值
        minXValue: 5, // X轴最小值
        minYValue: 5, // Y轴最小值
        name: "A", // 平台名称
        warmXValue: 40, // X轴预警值
        warmYValue: 26, // Y轴预警值
      },
      {
        isUse: false, // 是否启用
        maxXValue: 45, // X轴最大值
        maxYValue: 45, // Y轴最大值
        minXValue: 5, // X轴最小值
        minYValue: 5, // Y轴最小值
        name: "B", // 平台名称
        warmXValue: 14, // X轴预警值
        warmYValue: 14, // Y轴预警值
      },
    ],
    // 雷达相关参数
    radarValues: {
      isAntiCollisionKillStopSwitch: false, // 防碰撞急停开关
      loaderLength: 8.53, // 装载机长度
      roundToRound: 2.15, // 轮到轮距离
      maxWidth: 2.85, // 最大宽度
      radarGreenValue: 8.8, // 雷达绿色阈值
      radarRedValue: 1.6, // 雷达红色阈值
      radarYellowValue: 4.1, // 雷达黄色阈值
      valueLinkPointToRearDistance: 2.36, // 链接点到后部距离
    },
  },
  console: {
    changeLanguage: true,
    isShowNetwork: true,
    checkNetwork: {},
  },
  online: false,
  locked_opc: "66f4c31a8c6a933e2924143d",
  is_lock: 1,
};
