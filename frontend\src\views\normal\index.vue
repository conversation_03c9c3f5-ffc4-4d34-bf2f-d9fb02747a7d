<template>
  <v-container>
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title class="text-h5"> 常态化运行设置 </v-card-title>
          <v-card-text> </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import { ref } from "vue";

const autoRun = ref(false);
const runMode = ref("standard");
const runModes = [
  { title: "标准模式", value: "standard" },
  { title: "节能模式", value: "eco" },
  { title: "高性能模式", value: "performance" },
];
const interval = ref(5);
</script>

<style scoped>
.v-card {
  margin-bottom: 20px;
}
.v-col {
  margin-bottom: 16px;
}
</style>
