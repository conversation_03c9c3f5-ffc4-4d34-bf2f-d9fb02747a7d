<template>
  <v-container>
    <BackButton />

    <!-- 基础参数 -->
    <v-row>
      <v-col cols="12">
        <v-card class="mb-4">
          <v-card-title class="text-h6">Base</v-card-title>
          <v-card-text>
            <v-row>
              <template v-for="(value, key) in params" :key="key">
                <v-col v-if="key !== 'android'" cols="12" md="6" lg="4">
                  <v-switch
                    v-if="typeof value === 'boolean'"
                    v-model="params[key]"
                    :label="key"
                    color="primary"
                    hide-details
                    class="mb-3"
                  />
                  <Keyboard v-else v-model="params[key]" :label="key" outlined hide-details class="mb-3" />
                </v-col>
              </template>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
      <!-- pad参数 -->
      <v-col cols="12">
        <v-card class="mb-4">
          <v-card-title class="text-h6">pad</v-card-title>
          <v-card-text>
            <template v-for="(subValue, subKey) in params['pad']">
              <v-card v-if="typeof subValue === 'object'" :key="subKey" class="mb-4" outlined>
                <v-card-title class="text-subtitle-1">{{ subKey }}</v-card-title>
                <v-card-text>
                  <v-row>
                    <v-col v-for="(childValue, childKey) in subValue" :key="childKey" cols="12" md="6" lg="6">
                      <v-switch
                        v-if="typeof childValue === 'boolean'"
                        v-model="params.pad[subKey][childKey]"
                        :label="childKey"
                        color="primary"
                        hide-details
                        class="mb-3"
                      />
                      <Keyboard
                        v-else
                        v-model="params.pad[subKey][childKey]"
                        :label="childKey"
                        outlined
                        hide-details
                        class="mb-3"
                      />
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>

              <v-row v-else class="mb-3">
                <v-col cols="12" md="6" lg="11">
                  <v-switch
                    v-if="typeof subValue === 'boolean'"
                    v-model="params.pad[subKey]"
                    :label="subKey"
                    color="primary"
                    hide-details
                  />
                  <Keyboard v-else v-model="params.pad[subKey]" :label="subKey" outlined hide-details />
                </v-col>
              </v-row>
            </template>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- Android参数 -->
      <v-col cols="12">
        <v-card class="mb-4">
          <v-card-title class="text-h6">Android</v-card-title>
          <v-card-text>
            <template v-for="(subValue, subKey) in params['android']">
              <v-card v-if="typeof subValue === 'object'" :key="subKey" class="mb-4" outlined>
                <v-card-title class="text-subtitle-1">{{ subKey }}</v-card-title>
                <v-card-text>
                  <v-row>
                    <v-col v-for="(childValue, childKey) in subValue" :key="childKey" cols="12" md="6" lg="6">
                      <v-switch
                        v-if="typeof childValue === 'boolean'"
                        v-model="params.android[subKey][childKey]"
                        :label="childKey"
                        color="primary"
                        hide-details
                        class="mb-3"
                      />
                      <Keyboard
                        v-else
                        v-model="params.android[subKey][childKey]"
                        :label="childKey"
                        outlined
                        hide-details
                        class="mb-3"
                      />
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>

              <v-row v-else class="mb-3">
                <v-col cols="12" md="6" lg="11">
                  <v-switch
                    v-if="typeof subValue === 'boolean'"
                    v-model="params.pad[subKey]"
                    :label="subKey"
                    color="primary"
                    hide-details
                  />
                  <Keyboard v-else v-model="params.pad[subKey]" :label="subKey" outlined hide-details />
                </v-col>
              </v-row>
            </template>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 底部按钮 -->
    <div class="fixed bottom-6 right-6 flex gap-4 transition-transform">
      <v-btn
        class="hover:scale-[1.02] shadow-lg"
        icon
        color="primary"
        variant="elevated"
        fab
        size="large"
        @click="handleReset"
      >
        <v-icon>mdi-refresh</v-icon>
      </v-btn>
      <v-btn
        class="hover:scale-[1.02] shadow-lg"
        icon
        color="primary"
        variant="elevated"
        fab
        size="large"
        @click="router.back()"
      >
        <v-icon>mdi-arrow-left</v-icon>
      </v-btn>
      <v-btn
        class="hover:scale-[1.02] shadow-lg"
        icon
        color="primary"
        variant="elevated"
        fab
        size="large"
        @click="scrollToTop"
      >
        <v-icon>mdi-arrow-up</v-icon>
      </v-btn>
      <v-btn
        class="hover:scale-[1.02] shadow-lg"
        icon
        color="primary"
        variant="elevated"
        fab
        size="large"
        @click="handleSave"
      >
        <v-icon>mdi-content-save</v-icon>
      </v-btn>
    </div>

    <!-- 确认对话框 -->
    <v-dialog v-model="dialogVisible" max-width="600">
      <v-card>
        <v-card-title>确认修改</v-card-title>
        <v-card-text>
          <v-list v-if="changedParams.length > 0">
            <v-list-item v-for="(item, index) in changedParams" :key="index">
              <v-list-item-content>
                <v-list-item-title>{{ item.path }}</v-list-item-title>
                <v-list-item-subtitle class="!leading-[1.2rem]"> 修改前：{{ item.oldValue }} </v-list-item-subtitle>
                <v-list-item-subtitle class="!leading-[1.2rem]"> 修改后：{{ item.newValue }} </v-list-item-subtitle>
              </v-list-item-content>
            </v-list-item>
          </v-list>
          <div v-else>没有参数被修改</div>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="grey darken-1" text @click="cancelChanges">取消更改</v-btn>
          <v-btn color="primary" text @click="confirmSave">确认</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script setup>
import { reactive, ref, onMounted } from "vue";
import Keyboard from "@/components/Keyboard/index.vue";
import { cloneDeep, isEqual } from "lodash-es";
import { ipc } from "@/utils/ipcRenderer";
import { useRouter } from "vue-router";

const params = reactive({});

const initialParams = ref({});
const dialogVisible = ref(false);
const changedParams = ref([]);
const router = useRouter();

const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: "smooth",
  });
};

const handleReset = async () => {
  await ipc.invoke("global-state:clear");
  await init();
};

const init = async () => {
  // 获取所有初始数据
  await Object.assign(params, {});
  const allData = await ipc.invoke("global-state:getAll");
  await Object.assign(params, allData);
  changedParams.value = [];
  initialParams.value = cloneDeep(params);
};

onMounted(() => {
  init();
});

const findChangedParams = (obj1, obj2, path = "") => {
  const changes = [];

  const compareObjects = (o1, o2, currentPath) => {
    // 排除不需要更新的参数
    if (currentPath === "intelligent" || currentPath === "handleConfig") {
      return;
    }

    for (const key in o1) {
      const newPath = currentPath ? `${currentPath}.${key}` : key;
      if (key === "projectName") {
        console.log(o1[key], typeof o1[key]);
        console.log(o2[key], typeof o2[key]);
      }

      if (typeof o1[key] === "object" && o1[key] !== null) {
        compareObjects(o1[key], o2[key], newPath);
      } else if (!isEqual(o1[key], o2[key])) {
        changes.push({
          path: newPath,
          oldValue: o1[key],
          newValue: o2[key],
        });
      }
    }
  };

  compareObjects(obj1, obj2, path);
  return changes;
};

const handleSave = () => {
  changedParams.value = findChangedParams(initialParams.value, params);
  dialogVisible.value = true;
};

const cancelChanges = () => {
  // 恢复初始状态
  Object.assign(params, cloneDeep(initialParams.value));
  changedParams.value = [];
  dialogVisible.value = false;
  console.log(initialParams.value);
};

const confirmSave = async () => {
  // 遍历修改的参数，逐个更新到全局状态
  for (const change of changedParams.value) {
    console.log(change.path);
    await ipc.invoke("global-state:set", change.path, change.newValue);
  }
  initialParams.value = cloneDeep(params);
  changedParams.value = [];
  dialogVisible.value = false;
};
</script>

<style scoped>
.fixed-bottom {
  position: fixed;
  bottom: 0;
  right: 0;
  left: 0;
  background: rgb(var(--v-theme-background));
  z-index: 1;
}
</style>
