"use strict";

const { logger } = require("ee-core/log");
const UnifiedCommunicationService = require("../service/unifiedCommunicationService");
const globalStateManager = require("../service/globalStateManager");

/**
 * 连接管理控制器
 * 提供连接状态查询、切换等功能的前端接口
 */
class ConnectionController {
  /**
   * 获取连接状态
   */
  async getStatus(args, event) {
    try {
      const status = UnifiedCommunicationService.getConnectionStatus();
      logger.debug("[ConnectionController] Connection status retrieved");
      return {
        success: true,
        data: status
      };
    } catch (error) {
      logger.error("[ConnectionController] Failed to get connection status:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取详细连接信息
   */
  async getDetailedInfo(args, event) {
    try {
      const info = UnifiedCommunicationService.getDetailedConnectionInfo();
      logger.debug("[ConnectionController] Detailed connection info retrieved");
      return {
        success: true,
        data: info
      };
    } catch (error) {
      logger.error("[ConnectionController] Failed to get detailed connection info:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 强制切换连接类型
   * @param {Object} args - 参数
   * @param {string} args.type - 连接类型 ('websocket' 或 'usb')
   */
  async forceSwitch(args, event) {
    const { type } = args;
    
    if (!type || !['websocket', 'usb'].includes(type)) {
      return {
        success: false,
        error: "Invalid connection type. Must be 'websocket' or 'usb'"
      };
    }
    
    try {
      const result = await UnifiedCommunicationService.forceSwitch(type);
      logger.info(`[ConnectionController] Force switch to ${type}: ${result ? 'success' : 'failed'}`);
      
      return {
        success: result,
        data: {
          type,
          switched: result,
          status: UnifiedCommunicationService.getConnectionStatus()
        }
      };
    } catch (error) {
      logger.error(`[ConnectionController] Failed to force switch to ${type}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 检查服务健康状态
   */
  async checkHealth(args, event) {
    try {
      const isHealthy = await UnifiedCommunicationService.isHealthy();
      logger.debug(`[ConnectionController] Health check result: ${isHealthy}`);
      
      return {
        success: true,
        data: {
          isHealthy,
          timestamp: Date.now(),
          status: UnifiedCommunicationService.getConnectionStatus()
        }
      };
    } catch (error) {
      logger.error("[ConnectionController] Health check failed:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取USB配置
   */
  async getUsbConfig(args, event) {
    try {
      const usbConfig = await globalStateManager.get("usb");
      logger.debug("[ConnectionController] USB config retrieved");
      
      return {
        success: true,
        data: usbConfig || {
          devicePath: "",
          enabled: true,
          autoDetect: true
        }
      };
    } catch (error) {
      logger.error("[ConnectionController] Failed to get USB config:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 更新USB配置
   * @param {Object} args - 参数
   * @param {string} args.devicePath - USB设备路径
   * @param {boolean} args.enabled - 是否启用USB连接
   * @param {boolean} args.autoDetect - 是否自动检测USB设备
   */
  async updateUsbConfig(args, event) {
    const { devicePath, enabled, autoDetect } = args;
    
    try {
      const currentConfig = await globalStateManager.get("usb") || {};
      
      const newConfig = {
        ...currentConfig,
        ...(devicePath !== undefined && { devicePath }),
        ...(enabled !== undefined && { enabled }),
        ...(autoDetect !== undefined && { autoDetect })
      };
      
      await globalStateManager.set("usb", newConfig);
      logger.info("[ConnectionController] USB config updated:", newConfig);
      
      return {
        success: true,
        data: newConfig
      };
    } catch (error) {
      logger.error("[ConnectionController] Failed to update USB config:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 列出可用的串口设备
   */
  async listSerialPorts(args, event) {
    try {
      const { SerialPort } = require("serialport");
      const ports = await SerialPort.list();
      
      logger.debug(`[ConnectionController] Found ${ports.length} serial ports`);
      
      return {
        success: true,
        data: ports.map(port => ({
          path: port.path,
          manufacturer: port.manufacturer,
          serialNumber: port.serialNumber,
          vendorId: port.vendorId,
          productId: port.productId
        }))
      };
    } catch (error) {
      logger.error("[ConnectionController] Failed to list serial ports:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 测试USB连接
   * @param {Object} args - 参数
   * @param {string} args.devicePath - 要测试的设备路径
   */
  async testUsbConnection(args, event) {
    const { devicePath } = args;
    
    if (!devicePath) {
      return {
        success: false,
        error: "Device path is required"
      };
    }
    
    try {
      const { SerialPort } = require("serialport");
      
      // 尝试打开串口进行测试
      const testPort = new SerialPort({
        path: devicePath,
        baudRate: 115200,
        autoOpen: false
      });
      
      const testResult = await new Promise((resolve, reject) => {
        testPort.open((error) => {
          if (error) {
            reject(error);
          } else {
            testPort.close(() => {
              resolve(true);
            });
          }
        });
      });
      
      logger.info(`[ConnectionController] USB connection test successful: ${devicePath}`);
      
      return {
        success: true,
        data: {
          devicePath,
          testResult: true,
          timestamp: Date.now()
        }
      };
    } catch (error) {
      logger.error(`[ConnectionController] USB connection test failed for ${devicePath}:`, error);
      return {
        success: false,
        error: error.message,
        data: {
          devicePath,
          testResult: false,
          timestamp: Date.now()
        }
      };
    }
  }

  /**
   * 重启通信服务
   */
  async restartService(args, event) {
    try {
      logger.info("[ConnectionController] Restarting unified communication service");
      
      // 停止服务
      await UnifiedCommunicationService.stop();
      
      // 等待一秒
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 重新启动服务
      await UnifiedCommunicationService.start();
      
      logger.info("[ConnectionController] Unified communication service restarted successfully");
      
      return {
        success: true,
        data: {
          restarted: true,
          timestamp: Date.now(),
          status: UnifiedCommunicationService.getConnectionStatus()
        }
      };
    } catch (error) {
      logger.error("[ConnectionController] Failed to restart service:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = ConnectionController;
