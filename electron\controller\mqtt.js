"use strict";

const { logger } = require("ee-core/log");
const mqttService = require("../service/mqttService");

class MqttController {
  /**
   * 所有方法接收两个参数
   * @param args 前端传的参数
   * @param event - ipc通信时才有值
   */

  /**
   * 连接到MQTT代理
   */
  async connect(args, event) {
    console.log("args", args);
    console.log("event", event);

    try {
      mqttService.connect(args);
    } catch (error) {
      logger.error("[mqttController] Failed to connect:", error);
      throw error;
    }
  }

  /**
   * 断开MQTT连接
   */
  async disconnect(args, event) {
    try {
      mqttService.disconnect();
    } catch (error) {
      logger.error("[mqttController] Failed to disconnect:", error);
      throw error;
    }
  }

  /**
   * 发送消息到指定主题
   * @param {Object} args - 包含topic和message的对象
   */
  async sendMessage(args, event) {
    try {
      const { topic, message } = args;
      if (!topic || !message) {
        throw new Error("Topic and message are required");
      }
      mqttService.sendMessage(topic, message);
    } catch (error) {
      logger.error("[mqttController] Failed to send message:", error);
      throw error;
    }
  }

  /**
   * 订阅指定主题
   * @param {Object} args - 包含topic的对象
   */
  async subscribe(args, event) {
    try {
      const { topic } = args;
      if (!topic) {
        throw new Error("Topic is required");
      }
      mqttService.subscribe(topic);
    } catch (error) {
      logger.error("[mqttController] Failed to subscribe:", error);
      throw error;
    }
  }

  /**
   * 取消订阅指定主题
   * @param {Object} args - 包含topic的对象
   */
  async unsubscribe(args, event) {
    try {
      const { topic } = args;
      if (!topic) {
        throw new Error("Topic is required");
      }
      mqttService.unsubscribe(topic);
    } catch (error) {
      logger.error("[mqttController] Failed to unsubscribe:", error);
      throw error;
    }
  }
}

module.exports = MqttController;
