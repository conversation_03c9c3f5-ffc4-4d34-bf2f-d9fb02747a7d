const parseConfig = {
  head: [
    {
      key: "featureValue",
      label: "特征位固定值",
      type: "",
      len: 2,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "protocolVersion",
      label: "传输协议版本",
      type: "",
      len: 1,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "vehicleType",
      label: "车辆类型标识",
      type: "",
      len: 1,
      isBigEndian: false,
      isShow: true,
    },
    { key: "headerLength", label: "头长度", type: "", len: 2, isBigEndian: false, isShow: true },
    {
      key: "targetAddress",
      label: "目标地址",
      type: "",
      len: 1,
      isBigEndian: false,
      isShow: true,
    },
    { key: "sourceAddress", label: "源地址", type: "", len: 1, isBigEndian: false, isShow: true },
    {
      key: "transmitCount",
      label: "传输计数",
      type: "",
      len: 1,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "dataUnitCount",
      label: "数据单元ID个数",
      type: "",
      len: 1,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "dataUnitLength",
      label: "数据单元长度",
      type: "",
      len: 2,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "timestamp",
      label: "时间戳(毫秒级)",
      type: "TIME",
      len: 8,
      isBigEndian: false,
      isShow: true,
    },
    { key: "reserved", label: "保留", type: "", len: 0, isBigEndian: false, isShow: true },
  ],
  ID8: [
    { key: "id", label: "id", type: "BIN", len: 2, isBigEndian: false, isShow: true },
    {
      key: "dataLength",
      label: "数据长度",
      type: "BIN",
      len: 2,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "emergencyBrake",
      label: "紧急制动",
      type: "BIN",
      len: 1,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "consoleEmergencyStop",
      label: "操作台急停",
      type: "BIN",
      len: 1,
      isBigEndian: false,
      isShow: true,
    },
    { key: "reserved1", label: "保留", type: "BIN", len: 1, isBigEndian: false, isShow: true },
    { key: "reserved2", label: "保留", type: "BIN", len: 1, isBigEndian: false, isShow: true },
    {
      key: "leftJoystickX",
      label: "左手柄X轴",
      type: "FLOAT",
      len: 4,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "leftJoystickY",
      label: "左手柄Y轴",
      type: "FLOAT",
      len: 4,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "leftJoystickZ",
      label: "左手柄Z轴或KX轴",
      type: "FLOAT",
      len: 4,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "leftJoystickKY",
      label: "左手柄KY轴",
      type: "FLOAT",
      len: 4,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "leftJoystickButtons",
      label: "左手柄按钮",
      type: "BIN",
      len: 2,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "rightJoystickX",
      label: "右手柄X轴",
      type: "FLOAT",
      len: 4,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "rightJoystickY",
      label: "右手柄Y轴",
      type: "FLOAT",
      len: 4,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "rightJoystickZ",
      label: "右手柄Z轴或KX轴",
      type: "FLOAT",
      len: 4,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "rightJoystickKY",
      label: "右手柄KY轴",
      type: "FLOAT",
      len: 4,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "rightJoystickButtons",
      label: "右手柄按钮",
      type: "BIN",
      len: 2,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "leftPedal",
      label: "左踏板",
      type: "FLOAT",
      len: 4,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "rightPedal",
      label: "右踏板",
      type: "FLOAT",
      len: 4,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "functionButtons",
      label: "功能按钮",
      type: "BIN",
      len: 8,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "simulatedLeftPedal",
      label: "模拟左脚踏",
      type: "FLOAT",
      len: 4,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "simulatedRightPedal",
      label: "模拟右脚踏",
      type: "FLOAT",
      len: 4,
      isBigEndian: false,
      isShow: true,
    },
  ],
  ID16384: [
    { key: "id", label: "id", type: "BIN", len: 2, isBigEndian: false, isShow: true },
    {
      key: "dataLength",
      label: "数据长度",
      type: "BIN",
      len: 2,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "emergencyBrakeStatus",
      label: "紧急制动状态",
      type: "BIN",
      len: 1,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "consoleStopStatus",
      label: "操作台急停状态",
      type: "BIN",
      len: 1,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "remoteControlStatus",
      label: "遥控使能状态",
      type: "BIN",
      len: 1,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "localStopStatus",
      label: "本地急停状态",
      type: "BIN",
      len: 1,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "functionButtonStatus",
      label: "功能按钮状态",
      type: "BIN",
      len: 4,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "engineSpeed",
      label: "发动机转速",
      type: "FLOAT",
      len: 4,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "fuelLevel",
      label: "燃油液位",
      type: "FLOAT",
      len: 4,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "oilPressure",
      label: "机油压力",
      type: "FLOAT",
      len: 4,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "batteryVoltage",
      label: "电池电压",
      type: "FLOAT",
      len: 4,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "waterTemperature",
      label: "水温",
      type: "FLOAT",
      len: 4,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "oilTemperature",
      label: "油温",
      type: "FLOAT",
      len: 4,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "fuelPressure",
      label: "燃油输送压力",
      type: "FLOAT",
      len: 4,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "coolantLevel",
      label: "冷却剂液位",
      type: "FLOAT",
      len: 4,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "cabinTemperature",
      label: "驾驶室温度",
      type: "FLOAT",
      len: 4,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "ambientTemperature",
      label: "环境气温",
      type: "FLOAT",
      len: 4,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "hydraulicOilTemperature",
      label: "液压油温度",
      type: "FLOAT",
      len: 4,
      isBigEndian: false,
      isShow: true,
    },
  ],
  ID11: [
    { key: "id", label: "id", type: "", len: 2, isBigEndian: false, isShow: true },
    { key: "dataLength", label: "数据长度", type: "", len: 2, isBigEndian: false, isShow: true },
    {
      key: "senderStatus",
      label: "发送者当前状态",
      type: "",
      len: 1,
      isBigEndian: false,
      isShow: true,
    },
    { key: "otherInfo", label: "其他信息", type: "", len: 3, isBigEndian: false, isShow: true },
  ],
  ID20: [
    { key: "id", label: "id", type: "BIN", len: 2, isBigEndian: false, isShow: true },
    {
      key: "dataLength",
      label: "数据长度",
      type: "BIN",
      len: 2,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "communicationType",
      label: "参数通讯类型",
      type: "BIN",
      len: 1,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "parameterPackageType",
      label: "参数包类型",
      type: "BIN",
      len: 2,
      isBigEndian: false,
      isShow: true,
    },
    { key: "reserved", label: "预留", type: "BIN", len: 1, isBigEndian: false, isShow: true },
    { key: "jsonData", label: "JSON数据", type: "BIN", len: 0, isBigEndian: false, isShow: true },
  ],
  ID2048: [
    { key: "id", label: "id", type: "BIN", len: 2, isBigEndian: false, isShow: true },
    {
      key: "dataLength",
      label: "数据长度",
      type: "BIN",
      len: 2,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "vehicleSubType",
      label: "子车辆类型",
      type: "BIN",
      len: 2,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "senderStatus",
      label: "发送者自身状态",
      type: "BIN",
      len: 1,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "vehicleKeyStatus",
      label: "车端关键状态",
      type: "BIN",
      len: 1,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "consoleKeyStatus",
      label: "操作台端关键状态",
      type: "BIN",
      len: 1,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "failureLevel",
      label: "失效等级",
      type: "BIN",
      len: 1,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "failureSource",
      label: "失效来源",
      type: "BIN",
      len: 1,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "failureType",
      label: "失效类型",
      type: "BIN",
      len: 1,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "leftJoystickButtons",
      label: "左手柄按钮",
      type: "BIN",
      len: 2,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "rightJoystickButtons",
      label: "右手柄按钮",
      type: "BIN",
      len: 2,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "customButtons",
      label: "自定义按钮",
      type: "BIN",
      len: 8,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "safetyButtons",
      label: "安全按钮",
      type: "BIN",
      len: 2,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "extendedSafetyFeatures",
      label: "扩展安全功能",
      type: "BIN",
      len: 2,
      isBigEndian: false,
      isShow: true,
    },
    {
      key: "extendedGeneralFeatures",
      label: "扩展普通功能",
      type: "BIN",
      len: 24,
      isBigEndian: false,
      isShow: true,
    },
  ],
};

function parse(message, msgType, index) {
  const data8 = Str2Uint8Array(message.toString("hex"));
  return parseData8(data8);
}

function parseData8(data) {
  if (!data || data.length < 20) {
    return "无效数据";
  }

  let res = {};
  let flag = 0;

  const dataView = new DataView(data.buffer, data.byteOffset, data.byteLength);

  // 解析头部信息
  try {
    res = parseHeader(dataView, parseConfig.head);
    console.log(calculateHeaderLength(parseConfig.head), "calculateHeaderLength(parseConfig.head)");

    flag = calculateHeaderLength(parseConfig.head);
  } catch (error) {
    console.error("Error parsing header:", error);
    return null;
  }

  // 如果只有头部信息，直接返回
  if (data.byteLength === 20) return JSON.stringify(res);

  // 解析消息体
  try {
    const bodyOffset = res["headerLength"];
    const id_ = dataView.getUint16(bodyOffset || 0, true);
    console.log(id_, "id_");
    const config = parseConfig[`ID${id_}`];

    if (!config) {
      // 未知消息ID
      console.warn(`Unknown message ID: ${id_}`);
      return res;
    }

    res = {
      ...res,
      ...parseMessageBody(dataView, config, id_, flag),
    };
  } catch (error) {
    console.error("Error parsing message body:", error);
    return JSON.stringify(res);
  }

  return JSON.stringify(res);
}

function parseHeader(dataView, headerConfig) {
  const res = {};
  let flag = 0;

  for (const item of headerConfig) {
    try {
      res[item.key] = parseValue(dataView, item, flag);
      flag += item.len;
    } catch (error) {
      console.error(`Error parsing header field ${item.key}:`, error);
      throw error;
    }
  }

  return res;
}

function parseMessageBody(dataView, config, id_, startFlag) {
  const res = {};
  let flag = startFlag;

  // 通用的消息体解析
  for (const item of config) {
    try {
      res[item.label] = parseValue(dataView, item, flag);
      flag += item.len;
    } catch (error) {
      console.error(`Error parsing body field ${item.key}:`, error);
      continue;
    }
  }

  return res;
}

// execute(parse);
let a = parse(
  "0c10 2201 1400 0281 1803 6600 0ac8 81b0 9701 0000 0040 3800 0002 0200 0011 0000 0068 4744 0000 aa42 0000 9c43 cdcc d841 0000 7842 0000 0000 0000 0000 0000 0000 0000 0000 6666 3640 0000 f841 0020 0600 0000 0490 2800 0000 0000 0000 2a67 2dc2 6e0d 53e6 d600 0000 0000 b513 d700 0000 0000 e078 0100 0000 0000"
);
console.log("1111");

console.log(a);

function Str2Uint8Array(value) {
  let str = value.replace(/\s*/g, ""); // 先去除前后空格

  if (str.length === 0) return new Uint8Array(0); // 直接返回空数组
  if (str.length % 2 !== 0) str = "0" + str; // 确保长度为偶数

  const len = str.length / 2;

  if (len < 20) return new Uint8Array(0); // 直接返回空数组

  const data8 = new Uint8Array(len); // 直接分配 Uint8Array

  for (let i = 0; i < len; i++) {
    data8[i] = parseInt(str.substr(i * 2, 2), 16);
  }

  return data8;
}
function formatTimestamp(timestamp) {
  // 检查时间戳是否为秒级，如果是，则转换为毫秒
  if (timestamp.toString().length === 10) {
    timestamp *= 1000;
  }

  const date = new Date(timestamp);

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // 月份从 0 开始
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  const milliseconds = String(date.getMilliseconds()).padStart(3, "0"); // 毫秒部分

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
}
function parseValue(dataView, item, offset) {
  switch (item.len) {
    case 0:
      return "";
    case 1:
      return dataView.getUint8(offset);
    case 2:
      // 特征位特殊处理
      if (item.key === "featureValue") {
        return String.fromCharCode(dataView.getUint8(0)) + String.fromCharCode(dataView.getUint8(1));
      }
      return dataView.getUint16(offset, true);
    case 4:
      if (item.type === "FLOAT") {
        return dataView.getFloat32(offset, true);
      } else if (item.type === "BIN") {
        return dataView.getUint32(offset, true);
      }
      return dataView.getUint32(offset, true);
    case 8:
      if (item.type === "TIME") {
        const timestamp = dataView.getBigUint64(offset, true);
        return formatTimestamp(parseInt(timestamp.toString()));
      }
      return `${dataView.getUint16(offset, true)},${dataView.getUint16(
        offset + 2,
        true
      )},${dataView.getUint16(offset + 4, true)},${dataView.getUint16(offset + 6, true)}`;
    default:
      return null;
  }
}
function calculateHeaderLength(headerConfig) {
  return headerConfig.reduce((sum, item) => sum + item.len, 0);
}
function parseJsonData(dataView, offset) {
  const dataViewOffset = dataView.buffer.slice(offset);
  const decoder = new TextDecoder("utf-8");
  const jsonString = decoder.decode(dataViewOffset);
  return JSON.parse(jsonString);
}
