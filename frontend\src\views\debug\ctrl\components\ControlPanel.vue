<template>
  <v-card class="pa-4">
    <v-card-title class="text-h5 mb-4">STM32 控制配置</v-card-title>
    <v-form ref="form" v-model="isFormValid">
      <v-row>
        <v-col cols="12" md="6">
          <Keyboard v-model.trim="formData.stm32Ip" label="STM32 IP Address" :rules="ipRules" outlined dense />
        </v-col>
        <v-col cols="12" md="6">
          <Keyboard v-model.trim="formData.stm32SubnetMast" label="STM32 Subnet Mask" :rules="ipRules" outlined dense />
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="12" md="6">
          <Keyboard v-model.trim="formData.stm32Gateway" label="STM32 Gateway" :rules="ipRules" outlined dense />
        </v-col>
        <v-col cols="12" md="6">
          <Keyboard v-model.trim="formData.stm32Mac" label="STM32 MAC Address" :rules="macRules" outlined dense />
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="12" md="6">
          <Keyboard v-model.trim="formData.ip" label="Android IP Address" :rules="ipRules" outlined dense />
        </v-col>
        <v-col cols="12" md="6">
          <Keyboard v-model.trim="formData.port" label="Android Port" :rules="portRules" outlined dense />
        </v-col>
      </v-row>
      <v-row class="mt-4">
        <v-col cols="12" class="d-flex justify-center">
          <v-btn color="error" class="mr-4" @click="resetForm"> 重置 </v-btn>
          <v-btn color="primary" :disabled="!isFormValid" @click="saveForm"> 保存 </v-btn>
        </v-col>
      </v-row>
    </v-form>
  </v-card>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from "vue";
import Keyboard from "@/components/Keyboard/index.vue";
import { sendParams2Android, getParamsFromAndroid } from "@/utils/androidMessage";
import { registerWsHandler, unregisterWsHandler } from "@/utils/ipcMessageHandler";
import notificationService from "@/utils/notificationService";

// 默认配置值
const defaultConfig = {
  stm32Ip: "*************",
  stm32SubnetMast: "*************",
  stm32Gateway: "************",
  ip: "*************",
  port: "6000",
  stm32Mac: "00:0a:3a:00:00:02",
};

const form = ref(null);
const isFormValid = ref(true);
const formData = reactive({ ...defaultConfig });
const lastReceivedData = ref(null);

// 验证规则
const ipRules = [
  (v) => !!v || "IP address cannot be empty",
  (v) => /^(\d{1,3}\.){3}\d{1,3}$/.test(v) || "Invalid IP address format",
  (v) => v.split(".").every((part) => parseInt(part) >= 0 && parseInt(part) <= 255) || "IP address range is incorrect",
];

const macRules = [
  (v) => !!v || "MAC address cannot be empty",
  (v) => /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/.test(v) || "Invalid MAC address format",
];

const portRules = [
  (v) => !!v || "Port cannot be empty",
  (v) => /^\d+$/.test(v) || "Port must be a number",
  (v) => (parseInt(v) >= 0 && parseInt(v) <= 65535) || "Port must be between 0-65535",
];

// 重置表单
const resetForm = () => {
  // 如果有从WebSocket接收到的数据，则使用它，否则使用默认配置
  Object.assign(formData, lastReceivedData.value || defaultConfig);
  form.value?.resetValidation();
  notificationService.success("重置成功");
};

// 保存表单
const saveForm = async () => {
  if (!isFormValid.value) return;

  try {
    // 发送到Android设备
    await sendParams2Android("control.stm32", JSON.parse(JSON.stringify(formData)));
  } catch (error) {
    console.error("Failed to save STM32 configuration:", error);
  }
};

const handleWsMessage = (_, msg) => {
  if (
    msg.jsonData &&
    msg.communicationType === 0 &&
    msg.jsonData.page === 310 &&
    msg.jsonData.payload &&
    msg.jsonData.payload.route === "control.stm32"
  ) {
    lastReceivedData.value = { ...msg.jsonData.payload.value };
    Object.assign(formData, msg.jsonData.payload.value);
    notificationService.success("获取数据成功");
  }
};

let handlerId = null;

onMounted(() => {
  // 注册WebSocket消息处理器
  handlerId = registerWsHandler(310, handleWsMessage);

  const loadInitialData = async () => {
    try {
      // 请求Android设备发送STM32配置数据
      await getParamsFromAndroid("control.stm32", 310);

      if (!lastReceivedData.value) {
        Object.assign(formData, defaultConfig);
      }
    } catch (error) {
      console.error("Failed to load STM32 configuration:", error);
    }
  };
  loadInitialData();
});

onUnmounted(() => {
  if (handlerId) {
    unregisterWsHandler(handlerId);
  }
});
</script>

<style scoped></style>
