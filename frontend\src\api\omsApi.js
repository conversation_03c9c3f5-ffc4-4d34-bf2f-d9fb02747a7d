import { get, post, put, del } from "./index";

export const getLayoutList = (vehicleID) => {
  return get(
    `/vehicles/${vehicleID}/service/mix_template`,
    {},
    { headers: { authorization: "Bearer t-05393aef3c9871ec3eb5759fbde7c329d668d05433eb" } }
  );
};

export const setLayout = (vehicleID, layout) => {
  return put(`/vehicles/${vehicleID}/service/mix_layout`, layout, {
    headers: { authorization: "Bearer t-05393aef3c9871ec3eb5759fbde7c329d668d05433eb" },
  });
};
