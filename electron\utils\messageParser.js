"use strict";

/**
 * 消息解析工具类
 * @class
 *
 * @example WebSocket消息格式
 * 0x0C 0x10 0x22 0x04 0x14 0x00 0x81 0x02 0x00 0x01 0x50 0x30 0xFC 0xAF 0x7F 0x60 0x95 0x01 0x00 0x00
 *
 * @example MQTT消息格式
 * // Buffer输出
 * <Buffer 0c 10 22 04 14 00 81 02 00 01 50 30 fc af 7f 60 95 01 00 00>
 *
 * // Buffer.toString("hex")字符串输出
 * 0c10220114008102a5014400ac8b3064950100000800440001010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001100000000000080000000000000000
 */
class MessageParser {
  static _usbBuffer = new Uint8Array(0);

  /**
   * 解析消息
   * @param {string} message - 消息内容
   * @returns {object} 解析后的消息对象
   */

  static parse(message, type = "mqtt") {
    try {
      if (type === "mqtt") {
        const data8 = MessageParser.Str2Uint8Array(message.toString("hex"));
        return MessageParser.parseData8(data8);
      } else if (type === "websocket") {
        const cleanHex = message
          .replace(/0x|\n/g, "") // 删除 0x 和换行符
          .replace(/\s+/g, "") // 删除所有空格
          .trim(); // 去除首尾空白
        const data8 = MessageParser.Str2Uint8Array(cleanHex);
        console.log("data8", data8);
        return MessageParser.parseData8(data8);
      } else if (type === "usb") {
        return MessageParser.parseUsbMessage(message);
      }
    } catch (error) {
      console.error("Failed to parse message:", error);
      return null;
    }
  }

  /**
   * 将字符串转换为 Uint8Array
   * @param {string} value - 要转换的字符串
   * @returns {Uint8Array} 转换后的 Uint8Array
   */
  static Str2Uint8Array(value) {
    let str = value.replace(/\s*/g, ""); // 先去除前后空格

    if (str.length === 0) return new Uint8Array(0); // 直接返回空数组
    if (str.length % 2 !== 0) str = "0" + str; // 确保长度为偶数

    const len = str.length / 2;

    if (len < 20) return new Uint8Array(0); // 直接返回空数组

    const data8 = new Uint8Array(len); // 直接分配 Uint8Array

    for (let i = 0; i < len; i++) {
      data8[i] = parseInt(str.substr(i * 2, 2), 16);
    }

    return data8;
  }

  static parseData8(data) {
    const { parseConfig } = require("./messageDict");

    if (!data || data.length < 20) {
      console.error("Invalid data length");
      return null;
    }

    let res = {};
    let flag = 0;

    const dataView = new DataView(data.buffer, data.byteOffset, data.byteLength);

    // 解析头部信息
    try {
      res = MessageParser.parseHeader(dataView, parseConfig.head);
      flag = MessageParser.calculateHeaderLength(parseConfig.head);
    } catch (error) {
      console.error("Error parsing header:", error);
      return null;
    }

    // 如果只有头部信息，直接返回
    if (data.byteLength === 20) return res;

    // 解析消息体
    try {
      const bodyOffset = res["headerLength"];
      const id_ = dataView.getUint16(bodyOffset || 0, true);
      const config = parseConfig[`ID${id_}`];

      if (!config) {
        // 未知消息ID
        // console.warn(`Unknown message ID: ${id_}`);
        return res;
      }

      res = {
        ...res,
        ...MessageParser.parseMessageBody(dataView, config, id_, flag),
      };
    } catch (error) {
      console.error("Error parsing message body:", error);
      return res;
    }

    return res;
  }

  static parseHeader(dataView, headerConfig) {
    const res = {};
    let flag = 0;

    for (const item of headerConfig) {
      try {
        res[item.key] = MessageParser.parseValue(dataView, item, flag);
        if (item.key === "id") console.log("parseHeader ID", res[item.key]);
        flag += item.len;
      } catch (error) {
        console.error(`Error parsing header field ${item.key}:`, error);
        throw error;
      }
    }

    return res;
  }

  static parseMessageBody(dataView, config, id_, startFlag) {
    const res = {};
    let flag = startFlag;

    // 通用的消息体解析
    for (const item of config) {
      try {
        if (id_ === 11 && item.key === "otherInfo") {
          res[item.key] = MessageParser.parseStatusInfo(dataView, flag, res["senderStatus"]);
        } else if (id_ === 20 && item.key === "jsonData") {
          res[item.key] = MessageParser.parseJsonData(dataView, flag);
        } else {
          res[item.key] = MessageParser.parseValue(dataView, item, flag);
        }
        flag += item.len;
      } catch (error) {
        console.error(`Error parsing body field ${item.key}:`, error);
        continue;
      }
    }

    return res;
  }

  static parseValue(dataView, item, offset) {
    switch (item.len) {
      case 0:
        return "";
      case 1:
        return dataView.getUint8(offset);
      case 2:
        // 特征位特殊处理
        if (item.key === "featureValue") {
          return String.fromCharCode(dataView.getUint8(0)) + String.fromCharCode(dataView.getUint8(1));
        }
        return dataView.getUint16(offset, true);
      case 4:
        if (item.type === "FLOAT") {
          return dataView.getFloat32(offset, true);
        } else if (item.type === "BIN") {
          return dataView.getUint32(offset, true);
        }
        return dataView.getUint32(offset, true);
      case 8:
        if (item.type === "TIME") {
          const timestamp = dataView.getBigUint64(offset, true);
          return MessageParser.formatTimestamp(parseInt(timestamp.toString()));
        }
        return `${dataView.getUint16(offset, true)},${dataView.getUint16(
          offset + 2,
          true
        )},${dataView.getUint16(offset + 4, true)},${dataView.getUint16(offset + 6, true)}`;
      case 24:
        if (item.key === "extendedGeneralFeatures") {
          const buttons = [];
          // 解析前3字节的12个按钮状态
          for (let i = 0; i < 3; i++) {
            const byte = dataView.getUint8(offset + i);
            for (let j = 0; j < 4; j++) {
              buttons.push((byte >> (j * 2)) & 0x03);
            }
          }

          // 获取触摸点数量
          const touchCount = dataView.getUint8(offset + 3);

          // 解析5组触摸坐标
          const touchPoints = [];
          for (let i = 0; i < 5; i++) {
            const x = dataView.getUint16(offset + 4 + i * 4, true);
            const y = dataView.getUint16(offset + 4 + i * 4 + 2, true);
            touchPoints.push({ x, y });
          }

          return {
            buttons,
            touchCount,
            touchPoints,
          };
        }
        return null;
      default:
        return null;
    }
  }

  static parseStatusInfo(dataView, offset, senderStatus) {
    const statusInfo = {};
    const byte1 = dataView.getUint8(offset);
    const byte2 = dataView.getUint8(offset + 1);
    const byte3 = dataView.getUint8(offset + 2);

    switch (senderStatus) {
      case 8: // 初始化
        statusInfo.parameterStatus = !!(byte1 & 0x01);
        statusInfo.modeType = byte1 & 0x02 ? "远程模式" : "本地模式";
        statusInfo.moduleStatus = MessageParser.parseModuleStatus(byte1, byte2);
        break;
      case 32: // 等待
        statusInfo.vehicleTypeSpecified = !!(byte1 & 0x80);
        break;
      case 48: // 登录
        statusInfo.subVehicleType = (byte2 << 8) | byte1;
        break;
      case 64: // 远控
      case 80: // 锁定
      case 88: // 失效保护
        statusInfo.serialInfo = MessageParser.parseSerialInfo(byte1, byte2, byte3);
        break;
    }

    return statusInfo;
  }

  static parseJsonData(dataView, offset) {
    const dataViewOffset = dataView.buffer.slice(offset);
    const decoder = new TextDecoder("utf-8");
    const jsonString = decoder.decode(dataViewOffset);
    return JSON.parse(jsonString);
  }

  static parseModuleStatus(byte1, byte2) {
    const getModuleStatus = (bits) => {
      const statusMap = {
        0: "未连接",
        1: "已连接未装订参数",
        2: "已连接已装订参数",
        3: "已连接已完成初始化",
      };
      return statusMap[bits] || "未知状态";
    };

    return {
      module1: getModuleStatus((byte1 >> 4) & 0x03),
      module2: getModuleStatus((byte1 >> 6) & 0x03),
      module3: getModuleStatus(byte2 & 0x03),
      module4: getModuleStatus((byte2 >> 2) & 0x03),
    };
  }

  static parseSerialInfo(byte1, byte2, byte3) {
    return {
      year: byte1,
      month: byte2,
      number: byte3,
      extended: {
        month: (byte2 >> 4) & 0x0f,
        number: ((byte2 & 0x0f) << 8) | byte3,
      },
    };
  }

  static calculateHeaderLength(headerConfig) {
    return headerConfig.reduce((sum, item) => sum + item.len, 0);
  }

  static formatTimestamp(timestamp) {
    // 检查时间戳是否为秒级，如果是，则转换为毫秒
    if (timestamp.toString().length === 10) {
      timestamp *= 1000;
    }

    const date = new Date(timestamp);

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0"); // 月份从 0 开始
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const seconds = String(date.getSeconds()).padStart(2, "0");
    const milliseconds = String(date.getMilliseconds()).padStart(3, "0"); // 毫秒部分

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
  }

  /**
   * USB消息解析主方法
   * @param {Buffer|Uint8Array} rawData - 原始USB数据
   * @returns {object|null} 解析后的消息对象
   */
  static parseUsbMessage(rawData) {
    try {
      console.log("Parsing USB message, raw data length:", rawData.length);

      // 转换为Uint8Array
      let data;
      if (Buffer.isBuffer(rawData)) {
        data = new Uint8Array(rawData);
      } else if (rawData instanceof Uint8Array) {
        data = rawData;
      } else if (typeof rawData === "string") {
        // 如果是十六进制字符串，转换为Uint8Array
        const cleanHex = rawData.replace(/0x|\n|\s+/g, "").trim();
        data = MessageParser.Str2Uint8Array(cleanHex);
      } else {
        console.error("Unsupported USB data format");
        return null;
      }

      // Prepend any buffered data from previous calls
      const combinedData = new Uint8Array(this._usbBuffer.length + data.length);
      combinedData.set(this._usbBuffer, 0);
      combinedData.set(data, this._usbBuffer.length);

      // 查找完整的消息包
      const { packets, remainingData } = MessageParser.extractUsbPackets(combinedData);
      this._usbBuffer = remainingData;

      if (packets.length === 0) {
        console.log("No complete USB packets found");
        return null;
      }

      // 解析第一个完整的包（如果有多个包，只处理第一个）
      const packet = packets[0];
      console.log("Processing USB packet, length:", packet.length);

      // 验证和解包
      const unpackedData = MessageParser.unpackUsbMessage(packet);
      if (!unpackedData) {
        console.error("Failed to unpack USB message");
        return null;
      }

      // 使用现有的parseData8方法解析消息内容
      return MessageParser.parseData8(unpackedData);
    } catch (error) {
      console.error("Failed to parse USB message:", error);
      return null;
    }
  }

  /**
   * 从USB数据流中提取完整的消息包
   * @param {Uint8Array} data - USB数据流
   * @returns {Array<Uint8Array>} 完整的消息包数组
   */
  static extractUsbPackets(data) {
    const packets = [];
    const STX = 0x7e; // 起始符
    const ETX = 0x7d; // 终止符
    let searchOffset = 0;

    while (searchOffset < data.length) {
      // 查找起始符
      const startIndex = data.indexOf(STX, searchOffset);
      if (startIndex === -1) {
        // No more start bytes, the rest of the buffer is garbage.
        break;
      }

      // 查找对应的终止符
      const endIndex = data.indexOf(ETX, startIndex + 1);
      if (endIndex === -1) {
        // Found a start but no end. The rest of the buffer is an incomplete packet.
        // We should buffer from startIndex onwards.
        searchOffset = startIndex;
        break;
      }

      // 提取完整的包（包括起始符和终止符）
      const packetLength = endIndex - startIndex + 1;
      const packet = data.slice(startIndex, endIndex + 1);
      packets.push(packet);

      console.log(`Found USB packet: start=${startIndex}, end=${endIndex}, length=${packetLength}`);

      // 继续查找下一个包
      searchOffset = endIndex + 1;
    }

    // The remaining data is from the last search position to the end.
    const remainingData = data.slice(searchOffset);
    return { packets, remainingData };
  }

  /**
   * 解包USB消息（反转义 + CRC校验 + 移除封装）
   * @param {Uint8Array} packet - 完整的USB消息包
   * @returns {Uint8Array|null} 解包后的原始消息数据
   */
  static unpackUsbMessage(packet) {
    try {
      const STX = 0x7e;
      const ETX = 0x7d;

      // 验证包格式
      if (packet.length < 4 || packet[0] !== STX || packet[packet.length - 1] !== ETX) {
        console.error("Invalid USB packet format");
        return null;
      }

      // 提取封装内的数据（去除STX和ETX）
      const encapsulatedData = packet.slice(1, -1);

      // 反转义数据
      const unescapedData = MessageParser.unescapeUsbData(encapsulatedData);

      // 验证最小长度（至少要有2字节CRC）
      if (unescapedData.length < 2) {
        console.error("USB packet too short for CRC");
        return null;
      }

      // 分离数据和CRC
      const messageData = unescapedData.slice(0, -2);
      const receivedCrc = (unescapedData[unescapedData.length - 1] << 8) | unescapedData[unescapedData.length - 2];

      // 计算CRC校验
      const calculatedCrc = MessageParser.calculateCrc16(messageData);

      console.log(`CRC check: received=0x${receivedCrc.toString(16)}, calculated=0x${calculatedCrc.toString(16)}`);

      if (receivedCrc !== calculatedCrc) {
        console.error("USB message CRC check failed");
        return null;
      }

      console.log("USB message unpacked successfully, data length:", messageData.length);
      return messageData;
    } catch (error) {
      console.error("Error unpacking USB message:", error);
      return null;
    }
  }

  /**
   * USB数据反转义
   * @param {Uint8Array} data - 转义后的数据
   * @returns {Uint8Array} 反转义后的数据
   */
  static unescapeUsbData(data) {
    const result = [];
    const ESCAPE_CHAR = 0x7c;

    for (let i = 0; i < data.length; i++) {
      if (data[i] === ESCAPE_CHAR && i + 1 < data.length) {
        // 处理转义字符
        const nextByte = data[i + 1];
        switch (nextByte) {
          case 0x4e: // 0x7C 0x4E -> 0x7E
            result.push(0x7e);
            break;
          case 0x4d: // 0x7C 0x4D -> 0x7D
            result.push(0x7d);
            break;
          case 0x4c: // 0x7C 0x4C -> 0x7C
            result.push(0x7c);
            break;
          default:
            // 无效的转义序列，保持原样
            console.warn(`Invalid escape sequence: 0x${ESCAPE_CHAR.toString(16)} 0x${nextByte.toString(16)}`);
            result.push(data[i]);
            result.push(nextByte);
            break;
        }
        i++; // 跳过下一个字节
      } else {
        result.push(data[i]);
      }
    }

    return new Uint8Array(result);
  }

  /**
   * USB数据转义（用于发送）
   * @param {Uint8Array} data - 原始数据
   * @returns {Uint8Array} 转义后的数据
   */
  static escapeUsbData(data) {
    const result = [];
    const ESCAPE_CHAR = 0x7c;

    for (let i = 0; i < data.length; i++) {
      const byte = data[i];
      switch (byte) {
        case 0x7e: // 0x7E -> 0x7C 0x4E
          result.push(ESCAPE_CHAR, 0x4e);
          break;
        case 0x7d: // 0x7D -> 0x7C 0x4D
          result.push(ESCAPE_CHAR, 0x4d);
          break;
        case 0x7c: // 0x7C -> 0x7C 0x4C
          result.push(ESCAPE_CHAR, 0x4c);
          break;
        default:
          result.push(byte);
          break;
      }
    }

    return new Uint8Array(result);
  }

  /**
   * 计算CRC-16校验值（CCITT标准）
   * @param {Uint8Array} data - 要计算CRC的数据
   * @returns {number} CRC-16校验值
   */
  static calculateCrc16(data) {
    let crc = 0x0000;
    const MDTP_CRC16_TABLE = [
      0x0000, 0x1021, 0x2042, 0x3063, 0x4084, 0x50a5, 0x60c6, 0x70e7, 0x8108, 0x9129, 0xa14a, 0xb16b, 0xc18c, 0xd1ad,
      0xe1ce, 0xf1ef, 0x1231, 0x0210, 0x3273, 0x2252, 0x52b5, 0x4294, 0x72f7, 0x62d6, 0x9339, 0x8318, 0xb37b, 0xa35a,
      0xd3bd, 0xc39c, 0xf3ff, 0xe3de, 0x2462, 0x3443, 0x0420, 0x1401, 0x64e6, 0x74c7, 0x44a4, 0x5485, 0xa56a, 0xb54b,
      0x8528, 0x9509, 0xe5ee, 0xf5cf, 0xc5ac, 0xd58d, 0x3653, 0x2672, 0x1611, 0x0630, 0x76d7, 0x66f6, 0x5695, 0x46b4,
      0xb75b, 0xa77a, 0x9719, 0x8738, 0xf7df, 0xe7fe, 0xd79d, 0xc7bc, 0x48c4, 0x58e5, 0x6886, 0x78a7, 0x0840, 0x1861,
      0x2802, 0x3823, 0xc9cc, 0xd9ed, 0xe98e, 0xf9af, 0x8948, 0x9969, 0xa90a, 0xb92b, 0x5af5, 0x4ad4, 0x7ab7, 0x6a96,
      0x1a71, 0x0a50, 0x3a33, 0x2a12, 0xdbfd, 0xcbdc, 0xfbbf, 0xeb9e, 0x9b79, 0x8b58, 0xbb3b, 0xab1a, 0x6ca6, 0x7c87,
      0x4ce4, 0x5cc5, 0x2c22, 0x3c03, 0x0c60, 0x1c41, 0xedae, 0xfd8f, 0xcdec, 0xddcd, 0xad2a, 0xbd0b, 0x8d68, 0x9d49,
      0x7e97, 0x6eb6, 0x5ed5, 0x4ef4, 0x3e13, 0x2e32, 0x1e51, 0x0e70, 0xff9f, 0xefbe, 0xdfdd, 0xcffc, 0xbf1b, 0xaf3a,
      0x9f59, 0x8f78, 0x9188, 0x81a9, 0xb1ca, 0xa1eb, 0xd10c, 0xc12d, 0xf14e, 0xe16f, 0x1080, 0x00a1, 0x30c2, 0x20e3,
      0x5004, 0x4025, 0x7046, 0x6067, 0x83b9, 0x9398, 0xa3fb, 0xb3da, 0xc33d, 0xd31c, 0xe37f, 0xf35e, 0x02b1, 0x1290,
      0x22f3, 0x32d2, 0x4235, 0x5214, 0x6277, 0x7256, 0xb5ea, 0xa5cb, 0x95a8, 0x8589, 0xf56e, 0xe54f, 0xd52c, 0xc50d,
      0x34e2, 0x24c3, 0x14a0, 0x0481, 0x7466, 0x6447, 0x5424, 0x4405, 0xa7db, 0xb7fa, 0x8799, 0x97b8, 0xe75f, 0xf77e,
      0xc71d, 0xd73c, 0x26d3, 0x36f2, 0x0691, 0x16b0, 0x6657, 0x7676, 0x4615, 0x5634, 0xd94c, 0xc96d, 0xf90e, 0xe92f,
      0x99c8, 0x89e9, 0xb98a, 0xa9ab, 0x5844, 0x4865, 0x7806, 0x6827, 0x18c0, 0x08e1, 0x3882, 0x28a3, 0xcb7d, 0xdb5c,
      0xeb3f, 0xfb1e, 0x8bf9, 0x9bd8, 0xabbb, 0xbb9a, 0x4a75, 0x5a54, 0x6a37, 0x7a16, 0x0af1, 0x1ad0, 0x2ab3, 0x3a92,
      0xfd2e, 0xed0f, 0xdd6c, 0xcd4d, 0xbdaa, 0xad8b, 0x9de8, 0x8dc9, 0x7c26, 0x6c07, 0x5c64, 0x4c45, 0x3ca2, 0x2c83,
      0x1ce0, 0x0cc1, 0xef1f, 0xff3e, 0xcf5d, 0xdf7c, 0xaf9b, 0xbfba, 0x8fd9, 0x9ff8, 0x6e17, 0x7e36, 0x4e55, 0x5e74,
      0x2e93, 0x3eb2, 0x0ed1, 0x1ef0,
    ];

    for (const byte of data) {
      const index = ((crc >> 8) ^ byte) & 0xff;

      crc = (crc << 8) ^ MDTP_CRC16_TABLE[index];

      crc &= 0xffff;
    }

    return crc;
  }

  /**
   * 封装USB消息（用于发送）
   * @param {Uint8Array} messageData - 原始消息数据
   * @returns {Uint8Array} 封装后的USB消息
   */
  static packUsbMessage(messageData) {
    try {
      const STX = 0x7e;
      const ETX = 0x7d;

      // 计算CRC校验值
      const crc = MessageParser.calculateCrc16(messageData);

      // 创建包含CRC的数据
      const dataWithCrc = new Uint8Array(messageData.length + 2);
      dataWithCrc.set(messageData);
      dataWithCrc[messageData.length] = crc & 0xff; // CRC低字节
      dataWithCrc[messageData.length + 1] = (crc >> 8) & 0xff; // CRC高字节

      // 转义数据
      const escapedData = MessageParser.escapeUsbData(dataWithCrc);

      // 添加封装壳体
      const packedMessage = new Uint8Array(escapedData.length + 2);
      packedMessage[0] = STX;
      packedMessage.set(escapedData, 1);
      packedMessage[packedMessage.length - 1] = ETX;

      console.log(
        `USB message packed: original=${messageData.length}, with CRC=${dataWithCrc.length}, escaped=${escapedData.length}, final=${packedMessage.length}`
      );

      return packedMessage;
    } catch (error) {
      console.error("Error packing USB message:", error);
      return null;
    }
  }
}

module.exports = MessageParser;
