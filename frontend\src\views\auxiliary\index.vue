<template>
  <div class="aux-container">
    <iframe
      src="https://oms.apps.builderx.com/aid?VehicleId=65534ea229fc7fac5f4557e2&AuthToken=Bearer%20t-05393aef3c9871ec3eb5759fbde7c329d668d05433eb"
      class="aux-frame"
      sandbox="allow-scripts allow-same-origin allow-forms"
    ></iframe>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";

onMounted(() => {
  // 在这里可以添加iframe加载完成后的处理逻辑
});
</script>

<style scoped>
.aux-container {
  width: 1080px;
  height: 607px;
  margin: 32px auto 0;
  overflow: hidden;
  background-color: black !important;
}

.aux-frame {
  width: 100%;
  height: 100%;
  background-color: black !important;
}
</style>
