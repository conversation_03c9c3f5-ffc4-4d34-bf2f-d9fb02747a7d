<template>
  <div class="loading-demo">
    <h2 class="text-h4 mb-6">全局加载演示</h2>
    
    <v-card class="pa-4 mb-6">
      <v-card-title>加载类型</v-card-title>
      <v-card-text>
        <v-row>
          <v-col cols="12" sm="6" md="4">
            <v-btn color="primary" block @click="showDefaultLoading">默认加载</v-btn>
          </v-col>
          <v-col cols="12" sm="6" md="4">
            <v-btn color="secondary" block @click="showCircularLoading">圆形加载</v-btn>
          </v-col>
          <v-col cols="12" sm="6" md="4">
            <v-btn color="info" block @click="showLinearLoading">线性加载</v-btn>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
    
    <v-card class="pa-4 mb-6">
      <v-card-title>自动隐藏</v-card-title>
      <v-card-text>
        <v-row>
          <v-col cols="12" sm="6">
            <v-btn color="success" block @click="showAutoHideLoading">自动隐藏加载 (3秒)</v-btn>
          </v-col>
          <v-col cols="12" sm="6">
            <v-btn color="warning" block @click="showUpdateTextLoading">文本更新加载</v-btn>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
    
    <v-card class="pa-4 mb-6">
      <v-card-title>自定义加载</v-card-title>
      <v-card-text>
        <v-form @submit.prevent="showCustomLoading">
          <v-row>
            <v-col cols="12" sm="6" md="4">
              <v-text-field
                v-model="loadingText"
                label="加载文本"
                placeholder="请输入加载文本"
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="6" md="4">
              <v-select
                v-model="loadingType"
                :items="typeOptions"
                label="加载类型"
              ></v-select>
            </v-col>
            <v-col cols="12" sm="6" md="4">
              <v-select
                v-model="loadingColor"
                :items="colorOptions"
                label="加载颜色"
              ></v-select>
            </v-col>
            <v-col cols="12" sm="6" md="4">
              <v-text-field
                v-model.number="loadingDuration"
                label="显示时长(毫秒)"
                type="number"
                min="0"
                step="1000"
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="6" md="4">
              <v-switch
                v-model="autoHide"
                label="自动隐藏"
                color="primary"
              ></v-switch>
            </v-col>
            <v-col cols="12" sm="6" md="4">
              <v-text-field
                v-model.number="loadingOpacity"
                label="遮罩透明度"
                type="number"
                min="0"
                max="1"
                step="0.1"
              ></v-text-field>
            </v-col>
          </v-row>
          <v-btn color="primary" type="submit" block>显示自定义加载</v-btn>
        </v-form>
      </v-card-text>
    </v-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import loadingService from '@/utils/loadingService';

// 加载配置
const loadingText = ref('加载中...');
const loadingType = ref('default');
const loadingColor = ref('primary');
const loadingDuration = ref(3000);
const autoHide = ref(false);
const loadingOpacity = ref(0.8);

// 选项
const typeOptions = [
  { title: '默认', value: 'default' },
  { title: '圆形', value: 'circular' },
  { title: '线性', value: 'linear' }
];

const colorOptions = [
  { title: '主色', value: 'primary' },
  { title: '次要色', value: 'secondary' },
  { title: '成功', value: 'success' },
  { title: '信息', value: 'info' },
  { title: '警告', value: 'warning' },
  { title: '错误', value: 'error' }
];

// 显示不同类型的加载
const showDefaultLoading = () => {
  const loading = loadingService.default('默认加载中...');
  setTimeout(() => {
    loading.hide();
  }, 3000);
};

const showCircularLoading = () => {
  const loading = loadingService.circular('圆形加载中...');
  setTimeout(() => {
    loading.hide();
  }, 3000);
};

const showLinearLoading = () => {
  const loading = loadingService.linear('线性加载中...');
  setTimeout(() => {
    loading.hide();
  }, 3000);
};

// 自动隐藏加载
const showAutoHideLoading = () => {
  loadingService.autoHide('3秒后自动隐藏', 3000);
};

// 文本更新加载
const showUpdateTextLoading = () => {
  const loading = loadingService.default('初始加载文本');
  
  setTimeout(() => {
    loading.updateText('更新后的文本 (1/3)');
    
    setTimeout(() => {
      loading.updateText('更新后的文本 (2/3)');
      
      setTimeout(() => {
        loading.updateText('更新后的文本 (3/3)');
        
        setTimeout(() => {
          loading.hide();
        }, 1000);
      }, 1000);
    }, 1000);
  }, 1000);
};

// 显示自定义加载
const showCustomLoading = () => {
  const config = {
    text: loadingText.value,
    type: loadingType.value,
    color: loadingColor.value,
    opacity: loadingOpacity.value
  };
  
  if (autoHide.value) {
    config.autoHide = true;
    config.duration = loadingDuration.value;
    loadingService.show(config);
  } else {
    const loading = loadingService.show(config);
    setTimeout(() => {
      loading.hide();
    }, loadingDuration.value);
  }
};
</script>

<style scoped lang="scss">
.loading-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}
</style>
