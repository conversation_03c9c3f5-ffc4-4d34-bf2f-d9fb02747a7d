"use strict";

const { app } = require("electron");
const { logger } = require("ee-core/log");
const fs = require("fs");
const path = require("path");
const os = require("os");
const { ipcMain } = require("electron");
const { getMainWindow } = require("ee-core/electron");

/**
 * 内存监控服务
 * 用于监控Electron应用的内存使用情况，特别是主进程
 * 使用单例模式确保整个应用中只有一个实例
 */
class MemoryMonitorService {
  static instance = null;

  static getInstance() {
    if (!MemoryMonitorService.instance) {
      MemoryMonitorService.instance = new MemoryMonitorService();
    }
    return MemoryMonitorService.instance;
  }

  constructor() {
    // 如果已经存在实例，则返回该实例
    if (MemoryMonitorService.instance) {
      return MemoryMonitorService.instance;
    }

    // 否则创建新实例
    this.isMonitoring = false;
    this.monitorInterval = null;
    this.memoryData = [];
    this.memoryLogPath = path.join(app.getPath("userData"), "logs", "memory");
    this.currentLogFile = null;
    this.monitoringInterval = 60000; // 默认每分钟记录一次
    this.maxDataPoints = 1440; // 默认存储24小时的数据（每分钟一次）
    this.alertThreshold = 500; // 默认警告阈值为500MB
    this.autoHeapDump = false; // 是否自动生成堆快照
    this.heapDumpInterval = 3600000; // 堆快照间隔（默认1小时）
    this.lastHeapDumpTime = 0;

    // 确保日志目录存在
    this._ensureLogDirectory();

    // 保存实例
    MemoryMonitorService.instance = this;
  }

  /**
   * 确保日志目录存在
   * @private
   */
  _ensureLogDirectory() {
    if (!fs.existsSync(this.memoryLogPath)) {
      fs.mkdirSync(this.memoryLogPath, { recursive: true });
      logger.info(`[memoryMonitorService] Created memory log directory: ${this.memoryLogPath}`);
    }
  }

  /**
   * 开始内存监控
   * @param {Object} options 监控选项
   * @param {Number} options.interval 监控间隔（毫秒）
   * @param {Number} options.maxDataPoints 最大数据点数量
   * @param {Number} options.alertThreshold 警告阈值（MB）
   * @param {Boolean} options.autoHeapDump 是否自动生成堆快照
   * @param {Number} options.heapDumpInterval 堆快照间隔（毫秒）
   * @returns {Boolean} 是否成功启动监控
   */
  startMonitoring(options = {}) {
    if (this.isMonitoring) {
      logger.warn("[memoryMonitorService] Memory monitoring is already running");
      return false;
    }

    // 更新配置
    if (options.interval) this.monitoringInterval = options.interval;
    if (options.maxDataPoints) this.maxDataPoints = options.maxDataPoints;
    if (options.alertThreshold) this.alertThreshold = options.alertThreshold;
    if (options.autoHeapDump !== undefined) this.autoHeapDump = options.autoHeapDump;
    if (options.heapDumpInterval) this.heapDumpInterval = options.heapDumpInterval;

    // 创建新的日志文件
    this._createNewLogFile();

    // 立即执行一次内存检查
    this._checkMemory();

    // 设置定时检查
    this.monitorInterval = setInterval(() => {
      this._checkMemory();
    }, this.monitoringInterval);

    this.isMonitoring = true;
    logger.info(`[memoryMonitorService] Started memory monitoring with interval ${this.monitoringInterval}ms`);
    return true;
  }

  /**
   * 停止内存监控
   * @returns {Boolean} 是否成功停止监控
   */
  stopMonitoring() {
    if (!this.isMonitoring) {
      logger.warn("[memoryMonitorService] Memory monitoring is not running");
      return false;
    }

    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }

    this.isMonitoring = false;
    logger.info("[memoryMonitorService] Stopped memory monitoring");
    return true;
  }

  /**
   * 检查内存使用情况
   * @private
   */
  _checkMemory() {
    try {
      // 获取主进程内存使用情况
      const mainProcessMemory = this._getMainProcessMemory();

      // 获取所有进程内存使用情况
      const allProcessesMemory = this._getAllProcessesMemory();

      // 获取系统内存使用情况
      const systemMemory = this._getSystemMemory();

      // 组合数据
      const memorySnapshot = {
        timestamp: new Date().toISOString(),
        mainProcess: mainProcessMemory,
        allProcesses: allProcessesMemory,
        system: systemMemory
      };

      // 添加到内存数据数组
      this.memoryData.push(memorySnapshot);

      // 限制数据点数量
      if (this.memoryData.length > this.maxDataPoints) {
        this.memoryData.shift();
      }

      // 写入日志文件
      this._writeMemoryLog(memorySnapshot);

      // 检查是否超过警告阈值
      this._checkMemoryThreshold(mainProcessMemory);

      // 检查是否需要生成堆快照
      this._checkHeapDumpNeeded(mainProcessMemory);

      // 发送数据到渲染进程
      this._sendMemoryDataToRenderer(memorySnapshot);

      // 记录日志，便于调试
      logger.debug(`[memoryMonitorService] Memory data collected. Total entries: ${this.memoryData.length}`);

    } catch (error) {
      logger.error("[memoryMonitorService] Error checking memory:", error);
    }
  }

  /**
   * 获取主进程内存使用情况
   * @private
   * @returns {Object} 内存使用数据
   */
  _getMainProcessMemory() {
    const memoryData = process.memoryUsage();
    return {
      rss: Math.round(memoryData.rss / 1024 / 1024), // 常驻集大小 (MB)
      heapTotal: Math.round(memoryData.heapTotal / 1024 / 1024), // 总堆内存 (MB)
      heapUsed: Math.round(memoryData.heapUsed / 1024 / 1024), // 已用堆内存 (MB)
      external: Math.round(memoryData.external / 1024 / 1024), // 外部内存 (MB)
      arrayBuffers: Math.round((memoryData.arrayBuffers || 0) / 1024 / 1024) // ArrayBuffers内存 (MB)
    };
  }

  /**
   * 获取所有进程内存使用情况
   * @private
   * @returns {Array} 所有进程的内存使用数据
   */
  _getAllProcessesMemory() {
    try {
      const metrics = app.getAppMetrics();
      return metrics.map(metric => {
        return {
          pid: metric.pid,
          type: metric.type, // 'Browser' 表示主进程
          cpu: metric.cpu,
          memory: {
            workingSetSize: Math.round(metric.memory.workingSetSize / 1024 / 1024),
            peakWorkingSetSize: Math.round(metric.memory.peakWorkingSetSize / 1024 / 1024)
          }
        };
      });
    } catch (error) {
      logger.error("[memoryMonitorService] Error getting all processes memory:", error);
      return [];
    }
  }

  /**
   * 获取系统内存使用情况
   * @private
   * @returns {Object} 系统内存使用数据
   */
  _getSystemMemory() {
    try {
      // 使用Node.js的os模块获取系统内存信息
      // 这些API在Windows、macOS和Linux上都能工作
      const totalMem = Math.round(os.totalmem() / 1024 / 1024);
      const freeMem = Math.round(os.freemem() / 1024 / 1024);
      const usedMem = totalMem - freeMem;
      const memoryUsagePercent = Math.round((usedMem / totalMem) * 100);

      // 针对不同操作系统的特殊处理
      let platformSpecificInfo = {};

      if (process.platform === 'linux') {
        try {
          // 在Linux系统上，记录更详细的日志
          logger.debug(`[memoryMonitorService] Linux system detected. Memory info: Total=${totalMem}MB, Free=${freeMem}MB, Used=${usedMem}MB (${memoryUsagePercent}%)`);

          // 添加Linux特定的信息
          platformSpecificInfo = {
            platform: 'linux',
            kernelVersion: os.release(),
            cpuModel: os.cpus()[0]?.model || 'Unknown',
            cpuCount: os.cpus().length
          };
        } catch (linuxError) {
          logger.warn(`[memoryMonitorService] Error getting detailed Linux info: ${linuxError.message}`);
        }
      } else if (process.platform === 'win32') {
        // Windows特定信息
        platformSpecificInfo = {
          platform: 'windows',
          windowsRelease: os.release(),
          cpuModel: os.cpus()[0]?.model || 'Unknown',
          cpuCount: os.cpus().length
        };
      } else if (process.platform === 'darwin') {
        // macOS特定信息
        platformSpecificInfo = {
          platform: 'macos',
          osxRelease: os.release(),
          cpuModel: os.cpus()[0]?.model || 'Unknown',
          cpuCount: os.cpus().length
        };
      }

      // 返回系统内存信息
      return {
        total: totalMem,
        free: freeMem,
        used: usedMem,
        usagePercent: memoryUsagePercent,
        platform: process.platform,
        timestamp: new Date().toISOString(),
        ...platformSpecificInfo
      };
    } catch (error) {
      logger.error(`[memoryMonitorService] Error getting system memory: ${error.message}`);
      // 返回一个默认值，避免程序崩溃
      return {
        total: 0,
        free: 0,
        used: 0,
        usagePercent: 0,
        platform: process.platform,
        timestamp: new Date().toISOString(),
        error: error.message
      };
    }
  }

  /**
   * 创建新的日志文件
   * @private
   */
  _createNewLogFile() {
    const date = new Date();
    const dateStr = date.toISOString().split('T')[0];
    const fileName = `memory-${dateStr}.json`;
    this.currentLogFile = path.join(this.memoryLogPath, fileName);

    // 如果文件不存在，创建一个空的JSON数组
    if (!fs.existsSync(this.currentLogFile)) {
      fs.writeFileSync(this.currentLogFile, '[]', 'utf8');
    }

    logger.info(`[memoryMonitorService] Created new log file: ${this.currentLogFile}`);
  }

  /**
   * 写入内存日志
   * @private
   * @param {Object} memoryData 内存数据
   */
  _writeMemoryLog(memoryData) {
    try {
      if (!this.currentLogFile) {
        this._createNewLogFile();
      }

      // 检查日期是否变化，如果变化则创建新文件
      const currentDate = new Date().toISOString().split('T')[0];
      const fileDate = path.basename(this.currentLogFile).split('-')[1].split('.')[0];

      if (currentDate !== fileDate) {
        this._createNewLogFile();
      }

      // 读取现有数据
      const existingData = JSON.parse(fs.readFileSync(this.currentLogFile, 'utf8'));

      // 添加新数据
      existingData.push(memoryData);

      // 写回文件
      fs.writeFileSync(this.currentLogFile, JSON.stringify(existingData, null, 2), 'utf8');
    } catch (error) {
      logger.error("[memoryMonitorService] Error writing memory log:", error);
    }
  }

  /**
   * 检查内存是否超过警告阈值
   * @private
   * @param {Object} mainProcessMemory 主进程内存数据
   */
  _checkMemoryThreshold(mainProcessMemory) {
    if (mainProcessMemory.heapUsed > this.alertThreshold) {
      logger.warn(`[memoryMonitorService] Memory usage exceeded threshold: ${mainProcessMemory.heapUsed}MB > ${this.alertThreshold}MB`);

      // 发送警告到渲染进程
      const win = getMainWindow();
      if (win) {
        win.webContents.send('memory-monitor:alert', {
          type: 'warning',
          message: `内存使用超过阈值: ${mainProcessMemory.heapUsed}MB > ${this.alertThreshold}MB`,
          data: mainProcessMemory
        });
      }
    }
  }

  /**
   * 检查是否需要生成堆快照
   * @private
   * @param {Object} mainProcessMemory 主进程内存数据
   */
  _checkHeapDumpNeeded(mainProcessMemory) {
    const now = Date.now();

    // 如果启用了自动堆快照且已经过了指定的间隔时间
    if (this.autoHeapDump && (now - this.lastHeapDumpTime > this.heapDumpInterval)) {
      this.generateHeapSnapshot();
      this.lastHeapDumpTime = now;
    }

    // 如果内存使用超过阈值的150%，强制生成堆快照
    if (mainProcessMemory.heapUsed > this.alertThreshold * 1.5) {
      logger.warn(`[memoryMonitorService] Memory usage critically high: ${mainProcessMemory.heapUsed}MB. Generating heap snapshot.`);
      this.generateHeapSnapshot();
      this.lastHeapDumpTime = now;
    }
  }

  /**
   * 发送内存数据到渲染进程
   * @private
   * @param {Object} memoryData 内存数据
   */
  _sendMemoryDataToRenderer(memoryData) {
    const win = getMainWindow();
    if (win) {
      win.webContents.send('memory-monitor:update', memoryData);
    }
  }

  /**
   * 生成堆快照
   * @returns {String|Object} 堆快照文件路径或内存快照对象
   */
  async generateHeapSnapshot() {
    try {
      // 创建堆快照目录
      const heapdumpDir = path.join(this.memoryLogPath, 'snapshots');
      if (!fs.existsSync(heapdumpDir)) {
        fs.mkdirSync(heapdumpDir, { recursive: true });
      }

      // 生成文件名
      const timestamp = new Date().toISOString().replace(/:/g, '-');
      const snapshotPath = path.join(heapdumpDir, `memory-snapshot-${timestamp}.json`);

      // 尝试使用heapdump模块
      let heapdump;
      try {
        heapdump = require('heapdump');

        // 如果成功加载heapdump模块，使用它生成堆快照
        return new Promise((resolve, reject) => {
          const heapSnapshotPath = path.join(heapdumpDir, `heapdump-${timestamp}.heapsnapshot`);
          heapdump.writeSnapshot(heapSnapshotPath, (error, filename) => {
            if (error) {
              logger.error("[memoryMonitorService] Error generating heap snapshot:", error);
              // 如果heapdump失败，回退到简单的内存快照
              this._generateSimpleMemorySnapshot(snapshotPath)
                .then(filePath => resolve(filePath))
                .catch(err => reject(err));
            } else {
              logger.info(`[memoryMonitorService] Heap snapshot written to ${filename}`);
              resolve(filename);
            }
          });
        });
      } catch (error) {
        // 如果没有heapdump模块，生成简单的内存快照
        logger.warn("[memoryMonitorService] heapdump module not installed. Generating simple memory snapshot instead.");
        return this._generateSimpleMemorySnapshot(snapshotPath);
      }
    } catch (error) {
      logger.error("[memoryMonitorService] Error generating memory snapshot:", error);
      return null;
    }
  }

  /**
   * 生成简单的内存快照（不需要heapdump模块）
   * @private
   * @param {String} filePath 文件路径
   * @returns {String} 快照文件路径
   */
  async _generateSimpleMemorySnapshot(filePath) {
    try {
      // 获取主进程内存使用情况
      const mainProcessMemory = this._getMainProcessMemory();

      // 获取所有进程内存使用情况
      const allProcessesMemory = this._getAllProcessesMemory();

      // 获取系统内存使用情况
      const systemMemory = this._getSystemMemory();

      // 获取更详细的V8内存信息（如果可用）
      let v8Memory = {};
      try {
        const v8 = require('v8');
        v8Memory = {
          heapStatistics: v8.getHeapStatistics(),
          heapSpaceStatistics: v8.getHeapSpaceStatistics()
        };
      } catch (error) {
        logger.warn("[memoryMonitorService] v8 module not available for detailed heap statistics");
      }

      // 组合数据
      const memorySnapshot = {
        timestamp: new Date().toISOString(),
        mainProcess: mainProcessMemory,
        allProcesses: allProcessesMemory,
        system: systemMemory,
        v8: v8Memory,
        processInfo: {
          pid: process.pid,
          ppid: process.ppid,
          platform: process.platform,
          arch: process.arch,
          versions: process.versions,
          uptime: process.uptime()
        }
      };

      // 写入文件
      fs.writeFileSync(filePath, JSON.stringify(memorySnapshot, null, 2), 'utf8');
      logger.info(`[memoryMonitorService] Simple memory snapshot written to ${filePath}`);

      return filePath;
    } catch (error) {
      logger.error("[memoryMonitorService] Error generating simple memory snapshot:", error);
      throw error;
    }
  }
}

module.exports = MemoryMonitorService.getInstance();

