"use strict";

/**
 * 安卓和平板状态共用一套
 */
const SenderStatus = {
  /** 更新程序 */
  UPDATE: 0,
  /** 初始化 */
  INITIALIZE: 8,
  /** 预留 */
  RESERVED_1: 16,
  /** 预留 */
  RESERVED_2: 24,
  /** 等待 */
  WAITING: 32,
  /** 调试 */
  DEBUGGING: 40,
  /** 登录 */
  LOGIN: 48,
  /** 预留 */
  RESERVED_3: 56,
  /** 远控 */
  REMOTE_CONTROL: 64,
  /** 受控 */
  CONTROLLED: 72,
  /** 锁定 */
  LOCKED: 80,
  /** 失效保护 */
  FAILSAFE: 88,
  /** 观察 */
  OBSERVE: 128,
  /** 异步监测 */
  ASYNC_MONITORING: 160,
  /** 回环测试 */
  LOOPBACK_TEST: 192,
};

/**
 * 车辆类型
 */
const VehicleType = {
  /** 挖掘机 */
  EXCAVATOR: 1,
};

/**
 * 按钮类型
 */
const ButtonType = {
  /** 车端按钮 */
  CAR_BUTTON: 1,
  /** 布局按钮 */
  LAYOUT_BUTTON: 2,
};

module.exports = { SenderStatus, VehicleType, ButtonType };
