<template>
  <div class="notification-demo">
    <h2 class="text-h4 mb-6">全局通知演示</h2>
    
    <v-card class="pa-4 mb-6">
      <v-card-title>通知类型</v-card-title>
      <v-card-text>
        <v-row>
          <v-col cols="12" sm="6" md="3">
            <v-btn color="success" block @click="showSuccess">成功通知</v-btn>
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <v-btn color="info" block @click="showInfo">信息通知</v-btn>
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <v-btn color="warning" block @click="showWarning">警告通知</v-btn>
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <v-btn color="error" block @click="showError">错误通知</v-btn>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
    
    <v-card class="pa-4 mb-6">
      <v-card-title>显示模式</v-card-title>
      <v-card-text>
        <v-row>
          <v-col cols="12" sm="6">
            <v-btn color="primary" block @click="showSnackbar">Snackbar模式</v-btn>
          </v-col>
          <v-col cols="12" sm="6">
            <v-btn color="secondary" block @click="showEdge">边缘闪烁模式</v-btn>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
    
    <v-card class="pa-4 mb-6">
      <v-card-title>高级设置</v-card-title>
      <v-card-text>
        <v-row>
          <v-col cols="12" md="6">
            <v-text-field
              v-model="notificationContent"
              label="通知内容"
              placeholder="请输入通知内容"
            ></v-text-field>
          </v-col>
          <v-col cols="12" md="6">
            <v-select
              v-model="notificationType"
              label="通知类型"
              :items="typeOptions"
              item-title="text"
              item-value="value"
            ></v-select>
          </v-col>
          <v-col cols="12" md="4">
            <v-text-field
              v-model.number="duration"
              label="显示时长(毫秒)"
              type="number"
              min="0"
            ></v-text-field>
          </v-col>
          <v-col cols="12" md="4">
            <v-text-field
              v-model.number="count"
              label="显示次数(0表示无限)"
              type="number"
              min="0"
            ></v-text-field>
          </v-col>
          <v-col cols="12" md="4">
            <v-text-field
              v-model.number="frequency"
              label="显示频率(毫秒)"
              type="number"
              min="0"
            ></v-text-field>
          </v-col>
          <v-col cols="12" md="6">
            <v-select
              v-model="displayMode"
              label="显示模式"
              :items="modeOptions"
              item-title="text"
              item-value="value"
            ></v-select>
          </v-col>
          <v-col cols="12" md="6">
            <v-btn color="primary" block @click="showCustomNotification">显示自定义通知</v-btn>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
    
    <v-card class="pa-4">
      <v-card-title>模拟WebSocket消息</v-card-title>
      <v-card-text>
        <v-btn color="primary" block @click="simulateWsMessage">模拟接收WebSocket通知消息</v-btn>
      </v-card-text>
    </v-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { ipc } from '@/utils/ipcRenderer';
import notificationService from '@/utils/notificationService';

// 通知内容
const notificationContent = ref('这是一条测试通知');
const notificationType = ref('info');
const displayMode = ref('snackbar');
const duration = ref(3000);
const count = ref(1);
const frequency = ref(0);

// 选项
const typeOptions = [
  { text: '成功', value: 'success' },
  { text: '信息', value: 'info' },
  { text: '警告', value: 'warning' },
  { text: '错误', value: 'error' }
];

const modeOptions = [
  { text: 'Snackbar', value: 'snackbar' },
  { text: '边缘闪烁', value: 'edge' }
];

// 显示不同类型的通知
const showSuccess = () => {
  notificationService.success('操作成功完成！');
};

const showInfo = () => {
  notificationService.info('这是一条信息提示');
};

const showWarning = () => {
  notificationService.warning('请注意，这是一条警告信息');
};

const showError = () => {
  notificationService.error('操作失败，请重试');
};

// 显示不同模式的通知
const showSnackbar = () => {
  notificationService.info('这是Snackbar模式通知', { mode: 'snackbar' });
};

const showEdge = () => {
  notificationService.warning('这是边缘闪烁模式通知', { mode: 'edge', duration: 5000 });
};

// 显示自定义通知
const showCustomNotification = () => {
  notificationService.showNotification({
    type: notificationType.value,
    content: notificationContent.value,
    mode: displayMode.value,
    duration: duration.value,
    count: count.value,
    frequency: frequency.value
  });
};

// 模拟接收WebSocket消息
const simulateWsMessage = () => {
  // 直接调用controller方法发送通知
  ipc.invoke('controller/message/sendNotification', {
    type: notificationType.value,
    content: notificationContent.value,
    mode: displayMode.value,
    duration: duration.value,
    count: count.value,
    frequency: frequency.value
  });
};
</script>

<style scoped lang="scss">
.notification-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}
</style>
