"use strict";

const { logger } = require("ee-core/log");
const { getConfig } = require("ee-core/config");
const { getMainWindow } = require("ee-core/electron");
const mqttService = require("../service/mqttService");
const UnifiedCommunicationService = require("../service/unifiedCommunicationService");
const GlobalStateManager = require("../service/globalStateManager");
const networkService = require("../service/networkService");
const LogService = require("../service/logService");
const MemoryMonitorService = require("../service/memoryMonitorService");

class Lifecycle {
  /**
   * core app have been loaded
   */
  async ready() {
    logger.info("[lifecycle] ready");
    await GlobalStateManager.initialize();

    // 启动统一通信服务
    try {
      await UnifiedCommunicationService.start();
      logger.info("[lifecycle] Unified communication service started");
    } catch (error) {
      logger.error("[lifecycle] Failed to start unified communication service:", error);
    }

    // 可选：启动网络监控
    // await networkService.startMonitoring();

    // 可选：启动内存监控
    // MemoryMonitorService.startMonitoring({
    //   interval: 60000, // 每分钟记录一次
    //   maxDataPoints: 1440, // 保存24小时的数据
    //   alertThreshold: 500, // 警告阈值500MB
    //   autoHeapDump: false, // 默认不自动生成堆快照
    // });

    // 启动日志服务
    try {
      const logService = new LogService();
      // 启动日志服务，默认清理30天前的日志，每24小时执行一次
      logService.startAutoCleanup(30, 24 * 60 * 60 * 1000);
      logger.info("[lifecycle] Log service started");
    } catch (error) {
      logger.error("[lifecycle] Failed to start log service:", error);
    }
  }

  /**
   * electron app ready
   */
  async electronAppReady() {
    logger.info("[lifecycle] electron-app-ready");
  }

  /**
   * main window have been loaded
   */
  async windowReady() {
    logger.info("[lifecycle] window-ready");
    // 延迟加载，无白屏
    const { windowsOption } = getConfig();
    if (windowsOption.show == false) {
      const win = getMainWindow();
      win.once("ready-to-show", () => {
        win.show();
        win.focus();
      });
    }
  }

  /**
   * before app close
   */
  async beforeClose() {
    logger.info("[lifecycle] before-close");
    await GlobalStateManager.cleanup();
    mqttService.disconnect();

    // 停止统一通信服务
    try {
      await UnifiedCommunicationService.stop();
      logger.info("[lifecycle] Unified communication service stopped");
    } catch (error) {
      logger.error("[lifecycle] Failed to stop unified communication service:", error);
    }

    networkService.stopMonitoring();
    MemoryMonitorService.stopMonitoring();

    // 停止日志服务
    try {
      const logService = new LogService();
      logService.stopAutoCleanup();
      logger.info("[lifecycle] Log service stopped");
    } catch (error) {
      logger.error("[lifecycle] Failed to stop log service:", error);
    }
  }
}
Lifecycle.toString = () => "[class Lifecycle]";

module.exports = {
  Lifecycle,
};
