const initialSettings = {
  haveAttitudePerception: false, // 是否有姿态感知
  have_person_flag: false, // 是否有行人识别
  have_robot_anti_collision: false, // 是否有机器人防碰撞
  have_auto_reset: false, // 是否有自动复位
  have_bucket_landing_point: false, // 是否有斗着陆点
  have_bucket_tooth: false, // 是否有斗齿识别
  have_ground_level: false, // 是否有地面平整度
  have_dust_seen_through: false, // 是否有透尘功能
  have_remote_power_on: false, // 是否有远程上电
  have_side_profile_radar: false, // 是否有侧面轮廓雷达

  // 姿态感知
  attitude_perception: {
    color: "00ff3a", // 颜色
    form: 0, // 形状
    switch: 1, // 是否开启
    switch2: 1, // 声音 是否开启
    transparency: 100, // 透明度
  },
  // 有人标志配置
  has_person_flag: {
    color: "fefe3e", // 颜色
    form: 0, // 形状
    switch: 1, // 是否开启
    transparency: 0, // 透明度
  },
  // 防碰撞机器人配置
  aif_robot_anti_collision: {
    color: "00fffe", // 颜色
    form: 1, // 形状
    transparency: 100, // 透明度
    switch: 1, // 是否开启
    switch2: 0, // 声音开关
    switch3: 0, // 防碰撞刹车开关
  },
  // 自动复位配置
  auto_reset: {
    color: "00ff3a", // 颜色
    form: 0, // 形状
    switch: 1, // 是否开启
    transparency: 100, // 透明度
  },
  // 斗着陆点配置
  bucket_landing_point: {
    color: "fefe3e", // 颜色
    form: 0, // 形状 1-铲斗面 0-斗齿线
    switch: 1, // 是否开启
    transparency: 57, // 透明度
  },
  // 斗齿配置
  bucket_tooth: {
    color: "fefe3e", // 颜色
    form: 0, // 形状
    switch: 1, // 是否开启
    transparency: 0, // 透明度
  },
  // 地面标高配置
  ground_level: {
    color: "fefe3e", // 颜色
    form: 0, // 形状
    switch: 1, // 是否开启
    transparency: 0, // 透明度
  },
  // 粉尘透视
  aif_dust_seen_through: {
    color: "00fffe",
    form: 1,
    switch: 0,
    transparency: 78,
  },
  // 远程上电
  remote_power_on: {
    switch: 1, // 上电开关
    switch2: 2, // 下电开关
  },
  // 侧面轮廓雷达配置
  side_profile_radar: {
    color: "00ff3a", // 颜色
    form: 0, // 形状
    switch: 1, // 是否开启
    transparency: 100, // 透明度
  },
  // 倾斜报警平台配置
  tiltAlarmPlatform: [
    {
      isUse: true, // 是否启用
      maxXValue: 45, // X轴最大值
      maxYValue: 45, // Y轴最大值
      minXValue: 5, // X轴最小值
      minYValue: 5, // Y轴最小值
      name: "A", // 平台名称
      warmXValue: 40, // X轴预警值
      warmYValue: 26, // Y轴预警值
    },
    {
      isUse: false, // 是否启用
      maxXValue: 45, // X轴最大值
      maxYValue: 45, // Y轴最大值
      minXValue: 5, // X轴最小值
      minYValue: 5, // Y轴最小值
      name: "B", // 平台名称
      warmXValue: 14, // X轴预警值
      warmYValue: 14, // Y轴预警值
    },
  ],
  // 雷达相关参数
  radarValues: {
    loaderLength: 8.53, // 装载机长度
    roundToRound: 2.15, // 轮到轮距离
    maxWidth: 2.85, // 最大宽度
    valueLinkPointToRearDistance: 2.36, // 链接点到后部距离
    radarGreenValue: 8.8, // 雷达绿色阈值
    radarRedValue: 1.6, // 雷达红色阈值
    radarYellowValue: 4.1, // 雷达黄色阈值
  },
};
