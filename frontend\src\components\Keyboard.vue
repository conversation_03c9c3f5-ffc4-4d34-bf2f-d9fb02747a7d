<script setup>
import { ref, onMounted, watch, nextTick, onBeforeUnmount } from "vue";
import Keyboard from "simple-keyboard";
import "simple-keyboard/build/css/index.css";

const props = defineProps({
  modelValue: String,
});

const emit = defineEmits(["update:modelValue"]);

const isKeyboardVisible = ref(false);
const keyboard = ref(null);
const keyboardContainer = ref(null);
const inputRef = ref(null);

// 添加全局点击事件处理
const handleClickOutside = (event) => {
  const keyboardElement = keyboardContainer.value;
  const inputElement = inputRef.value?.$el;

  // 检查点击是否在键盘或输入框范围内
  const isInsideKeyboard = keyboardElement?.contains(event.target);
  const isInsideInput = inputElement?.contains(event.target);

  if (!isInsideKeyboard && !isInsideInput) {
    hideKeyboard();
  }
};

onMounted(async () => {
  await nextTick();

  keyboard.value = new Keyboard(keyboardContainer.value, {
    onChange: (input) => emit("update:modelValue", input),
    onKeyPress: (button) => {
      if (button === "{enter}") hideKeyboard();
    },
    physicalKeyboardHighlight: false,
    mergeDisplay: true,
    layout: {
      default: [
        "1 2 3 4 5 6 7 8 9 0 {bksp}",
        "q w e r t y u i o p",
        "a s d f g h j k l",
        "z x c v b n m {space}",
        "{shift} {lang} {enter}",
      ],
    },
  });

  hideKeyboard();
});

// 清理事件监听
onBeforeUnmount(() => {
  document.removeEventListener("mousedown", handleClickOutside);
});

const handleFocus = () => {
  showKeyboard();
  inputRef.value?.focus();
};

// 移除原有的blur处理
const showKeyboard = () => {
  if (!isKeyboardVisible.value) {
    isKeyboardVisible.value = true;
    keyboard.value.setInput(props.modelValue || "");
    // 添加全局点击监听
    document.addEventListener("mousedown", handleClickOutside);
  }
};

const hideKeyboard = () => {
  if (isKeyboardVisible.value) {
    isKeyboardVisible.value = false;
    // 移除全局点击监听
    document.removeEventListener("mousedown", handleClickOutside);
    inputRef.value?.blur();
  }
};

watch(
  () => props.modelValue,
  (newVal) => {
    keyboard.value?.setInput(newVal);
  }
);
</script>

<template>
  <v-text-field
    ref="inputRef"
    :model-value="modelValue"
    @focus="handleFocus"
    readonly
    v-bind="$attrs"
  />

  <div ref="keyboardContainer" class="simple-keyboard" :style="{ display: isKeyboardVisible ? 'block' : 'none' }"></div>
</template>

<style>
.simple-keyboard {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding: 8px;
  background: rgb(var(--v-theme-surface));
  transition: transform 0.3s ease;
}

/* 使用更具体的选择器来提高优先级 */
.simple-keyboard.hg-theme-default {
  background: rgb(var(--v-theme-surface));
}

.simple-keyboard.hg-theme-default .hg-button {
  color: rgb(var(--v-theme-on-surface)) !important;
  background-color: rgb(var(--v-theme-background)) !important;
}
</style>
