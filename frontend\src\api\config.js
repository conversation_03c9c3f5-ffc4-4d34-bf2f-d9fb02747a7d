import axios from "axios";
import { ipc } from "@/utils/ipc<PERSON>enderer";

let baseURL = "/api/v2";

async function initializeConfig() {
  try {
    if (import.meta.env.DEV) return;

    let omsUrl = await ipc.invoke("global-state:get", "android.platformParams.parameterPlatform");
    let omsPort = await ipc.invoke("global-state:get", "android.platformParams.parameterPlatformPort");
    let protocol = "http://";
    if (omsPort == "443") protocol = "https://";

    if (omsUrl && omsPort) {
      baseURL = `${protocol}${omsUrl}:${omsPort}/api/v2`;
    }

    axiosInstance.defaults.baseURL = baseURL;
  } catch (error) {

  }
}

const axiosInstance = axios.create({
  baseURL,
  timeout: 15000, // 15 seconds
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
});

axiosInstance.interceptors.request.use(
  (config) => {
    // const token = localStorage.getItem('token');
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

axiosInstance.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    const { response } = error;

    if (response) {
      switch (response.status) {
        case 401: // Unauthorized
          console.error("Unauthorized access");
          break;
        case 403: // Forbidden
          console.error("Access forbidden");
          break;
        case 404: // Not found
          console.error("Resource not found");
          break;
        case 500: // Server error
          console.error("Server error");
          break;
        default:
          console.error(`Error with status code: ${response.status}`);
      }

      return Promise.reject(response.data || "API Error");
    } else if (error.request) {
      console.error("No response received from server");
      return Promise.reject("Network error - no response");
    } else {
      console.error("Error setting up request:", error.message);
      return Promise.reject(error.message);
    }
  }
);

export { initializeConfig };
export default axiosInstance;
