<template>
  <v-card class="pa-4">
    <v-card-title class="text-h5 mb-4">车辆尺寸设置</v-card-title>

    <!-- 按钮区域 -->
    <v-row class="mb-6">
      <v-col cols="12" md="4"><v-btn class="w-[300px]" size="x-large" color="primary" @click="clearDuration">清空时长</v-btn></v-col>
      <v-col cols="12" md="4"><v-btn class="w-[300px]" size="x-large" color="primary" @click="deleteRosHandleValues">删除ros手柄值</v-btn></v-col>
      <v-col cols="12" md="4"><v-btn class="w-[300px]" size="x-large" color="primary" @click="deleteMainControlHandleValues">删除主控板手柄值</v-btn></v-col>
    </v-row>

    <!-- 表单区域 -->
    <v-form>
      <v-row>
        <v-col cols="12">
          <v-card class="mb-4" variant="outlined">
            <v-card-title class="text-subtitle-1">车辆尺寸参数</v-card-title>
            <v-card-text>
              <v-row>
                <v-col cols="12" md="6">
                  <Keyboard
                    v-model="vehicleLengthForm.loaderLength"
                    label="车辆长度(m)"
                    :rules="numericRules"
                    outlined
                    dense
                  />
                </v-col>
                <v-col cols="12" md="6">
                  <Keyboard
                    v-model="vehicleLengthForm.maxWidth"
                    label="车辆宽度(m)"
                    :rules="numericRules"
                    outlined
                    dense
                  />
                </v-col>
                <v-col cols="12" md="6">
                  <Keyboard
                    v-model="vehicleLengthForm.roundToRound"
                    label="轮距(m)"
                    :rules="numericRules"
                    outlined
                    dense
                  />
                </v-col>
                <v-col cols="12" md="6">
                  <Keyboard
                    v-model="vehicleLengthForm.valueLinkPointToRearDistance"
                    label="中心至尾部距离(m)"
                    :rules="numericRules"
                    outlined
                    dense
                  />
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-form>
  </v-card>
</template>

<script setup>
import { cloneDeep, isEqual } from "lodash-es";
import { reactive, onMounted, watch } from "vue";
import Keyboard from "@/components/Keyboard/index.vue";
import { ipc } from "@/utils/ipcRenderer";
import { sendParams2Android, sendEvent2Android } from "@/utils/androidMessage";
import { findDifferences } from "@/utils/diffUtils";

// 表单数据 遥控时长设置 1，2 姿态感知设置（跟车走） 1，2
const vehicleLengthForm = reactive({
  loaderLength: 8.53, // 长度
  maxWidth: 2.85, // 宽度
  roundToRound: 2.15, // 轮距
  valueLinkPointToRearDistance: 2.36, // 中心至尾部部距离
});

const numericRules = [(v) => !!v || "数值不能为空", (v) => !isNaN(Number(v)) || "必须是数字"];

const clearDuration = () => {
  sendEvent2Android("remoteDuration.clear");
};

const deleteRosHandleValues = () => {
  sendEvent2Android("delete.rosHandle");
};

const deleteMainControlHandleValues = () => {
  sendEvent2Android("delete.mainControlBoardHandle");
};

let prevSettings = null;

// 监听表单数据变化，参考DeviceData.vue的watch方式
watch(
  () => vehicleLengthForm,
  (newVal) => {
    if (prevSettings && !isEqual(prevSettings, newVal)) {
      let diffList = findDifferences(newVal, prevSettings);
      if (diffList.length > 0) {
        diffList.forEach((item) => {
          sendParams2Android("intelligent.radarValues." + item.path, item.newValue);
        });
      }
    }
    prevSettings = cloneDeep(newVal);
  },
  {
    deep: true,
  }
);

// 初始化
onMounted(async () => {
  try {
    const savedData = await ipc.invoke("global-state:get", "intelligent.radarValues");
    if (savedData && Object.keys(savedData).length > 0) {
      Object.assign(vehicleLengthForm, savedData);
      console.log("车辆尺寸配置已加载", savedData);
    }
  } catch (error) {
    console.error("获取车辆尺寸配置失败:", error);
  }

  // 初始化完成后设置初始值用于比较
  prevSettings = cloneDeep(vehicleLengthForm);
});
</script>

<style scoped>
.setting-control {
  max-width: 60px;
}
</style>
