<template>
  <v-card class="connection-monitor" elevation="2">
    <v-card-title class="d-flex align-center">
      <v-icon :color="statusColor" class="mr-2">{{ statusIcon }}</v-icon>
      <span>连接状态监控</span>
      <v-spacer></v-spacer>
      <v-btn
        icon
        size="small"
        @click="refreshStatus"
        :loading="loading"
      >
        <v-icon>mdi-refresh</v-icon>
      </v-btn>
    </v-card-title>

    <v-card-text>
      <!-- 当前活动连接 -->
      <v-row class="mb-4">
        <v-col cols="12">
          <v-alert
            :type="activeConnection ? 'success' : 'warning'"
            variant="tonal"
            class="mb-3"
          >
            <div class="d-flex align-center">
              <strong>当前连接：</strong>
              <span class="ml-2">
                {{ activeConnection ? getConnectionDisplayName(activeConnection) : '无连接' }}
              </span>
              <v-spacer></v-spacer>
              <v-chip
                :color="activeConnection ? 'success' : 'warning'"
                size="small"
                variant="flat"
              >
                {{ activeConnection ? '已连接' : '未连接' }}
              </v-chip>
            </div>
          </v-alert>
        </v-col>
      </v-row>

      <!-- 连接详情 -->
      <v-row>
        <v-col cols="12" md="6">
          <v-card variant="outlined" class="connection-card">
            <v-card-title class="d-flex align-center">
              <v-icon color="primary" class="mr-2">mdi-wifi</v-icon>
              <span>WebSocket 连接</span>
              <v-spacer></v-spacer>
              <v-chip
                :color="getStateColor(connections.websocket?.state)"
                size="small"
                variant="flat"
              >
                {{ getStateText(connections.websocket?.state) }}
              </v-chip>
            </v-card-title>
            <v-card-text>
              <div class="connection-info">
                <div class="info-item">
                  <span class="label">状态：</span>
                  <span :class="getStateClass(connections.websocket?.state)">
                    {{ getStateText(connections.websocket?.state) }}
                  </span>
                </div>
                <div class="info-item">
                  <span class="label">重连次数：</span>
                  <span>{{ connections.websocket?.reconnectAttempts || 0 }}</span>
                </div>
                <div class="info-item">
                  <span class="label">是否活动：</span>
                  <v-icon 
                    :color="connections.websocket?.isActive ? 'success' : 'grey'"
                    size="small"
                  >
                    {{ connections.websocket?.isActive ? 'mdi-check-circle' : 'mdi-circle-outline' }}
                  </v-icon>
                </div>
              </div>
              <v-btn
                v-if="!connections.websocket?.isActive"
                color="primary"
                size="small"
                variant="outlined"
                @click="switchConnection('websocket')"
                :loading="switching"
                class="mt-2"
              >
                切换到 WebSocket
              </v-btn>
            </v-card-text>
          </v-card>
        </v-col>

        <v-col cols="12" md="6">
          <v-card variant="outlined" class="connection-card">
            <v-card-title class="d-flex align-center">
              <v-icon color="orange" class="mr-2">mdi-usb</v-icon>
              <span>USB 连接</span>
              <v-spacer></v-spacer>
              <v-chip
                :color="getStateColor(connections.usb?.state)"
                size="small"
                variant="flat"
              >
                {{ getStateText(connections.usb?.state) }}
              </v-chip>
            </v-card-title>
            <v-card-text>
              <div class="connection-info">
                <div class="info-item">
                  <span class="label">状态：</span>
                  <span :class="getStateClass(connections.usb?.state)">
                    {{ getStateText(connections.usb?.state) }}
                  </span>
                </div>
                <div class="info-item">
                  <span class="label">重连次数：</span>
                  <span>{{ connections.usb?.reconnectAttempts || 0 }}</span>
                </div>
                <div class="info-item">
                  <span class="label">是否活动：</span>
                  <v-icon 
                    :color="connections.usb?.isActive ? 'success' : 'grey'"
                    size="small"
                  >
                    {{ connections.usb?.isActive ? 'mdi-check-circle' : 'mdi-circle-outline' }}
                  </v-icon>
                </div>
              </div>
              <v-btn
                v-if="!connections.usb?.isActive"
                color="orange"
                size="small"
                variant="outlined"
                @click="switchConnection('usb')"
                :loading="switching"
                class="mt-2"
              >
                切换到 USB
              </v-btn>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>

      <!-- 消息队列信息 -->
      <v-row class="mt-4" v-if="queuedMessages > 0">
        <v-col cols="12">
          <v-alert type="info" variant="tonal">
            <div class="d-flex align-center">
              <v-icon class="mr-2">mdi-message-outline</v-icon>
              <span>队列中有 {{ queuedMessages }} 条待发送消息</span>
            </div>
          </v-alert>
        </v-col>
      </v-row>

      <!-- 操作按钮 -->
      <v-row class="mt-4">
        <v-col cols="12">
          <div class="d-flex gap-2">
            <v-btn
              color="primary"
              variant="outlined"
              @click="openUsbConfig"
              prepend-icon="mdi-cog"
            >
              USB 配置
            </v-btn>
            <v-btn
              color="warning"
              variant="outlined"
              @click="restartService"
              :loading="restarting"
              prepend-icon="mdi-restart"
            >
              重启服务
            </v-btn>
            <v-btn
              color="info"
              variant="outlined"
              @click="checkHealth"
              :loading="checking"
              prepend-icon="mdi-heart-pulse"
            >
              健康检查
            </v-btn>
          </div>
        </v-col>
      </v-row>
    </v-card-text>

    <!-- USB配置对话框 -->
    <v-dialog v-model="usbConfigDialog" max-width="600">
      <v-card>
        <v-card-title>USB 连接配置</v-card-title>
        <v-card-text>
          <v-form ref="usbForm">
            <v-text-field
              v-model="usbConfig.devicePath"
              label="设备路径"
              placeholder="例如: /dev/ttyUSB0 或 COM3"
              prepend-icon="mdi-usb"
              clearable
            ></v-text-field>
            
            <v-switch
              v-model="usbConfig.enabled"
              label="启用 USB 连接"
              color="primary"
            ></v-switch>
            
            <v-switch
              v-model="usbConfig.autoDetect"
              label="自动检测 USB 设备"
              color="primary"
            ></v-switch>

            <v-btn
              color="info"
              variant="outlined"
              @click="listSerialPorts"
              :loading="listingPorts"
              prepend-icon="mdi-format-list-bulleted"
              class="mb-3"
            >
              列出可用端口
            </v-btn>

            <v-list v-if="availablePorts.length > 0" class="mt-3">
              <v-list-subheader>可用串口设备</v-list-subheader>
              <v-list-item
                v-for="port in availablePorts"
                :key="port.path"
                @click="selectPort(port.path)"
                class="port-item"
              >
                <template v-slot:prepend>
                  <v-icon>mdi-usb</v-icon>
                </template>
                <v-list-item-title>{{ port.path }}</v-list-item-title>
                <v-list-item-subtitle>
                  {{ port.manufacturer || '未知制造商' }}
                  <span v-if="port.vendorId">- VID: {{ port.vendorId }}</span>
                  <span v-if="port.productId">- PID: {{ port.productId }}</span>
                </v-list-item-subtitle>
              </v-list-item>
            </v-list>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="usbConfigDialog = false">取消</v-btn>
          <v-btn
            color="primary"
            @click="saveUsbConfig"
            :loading="savingConfig"
          >
            保存
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-card>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import { ipc } from '@/utils/ipcRenderer'

// 响应式数据
const loading = ref(false)
const switching = ref(false)
const restarting = ref(false)
const checking = ref(false)
const savingConfig = ref(false)
const listingPorts = ref(false)

const connectionStatus = ref({
  isStarted: false,
  activeConnection: null,
  connections: {},
  queuedMessages: 0
})

const usbConfigDialog = ref(false)
const usbConfig = reactive({
  devicePath: '',
  enabled: true,
  autoDetect: true
})

const availablePorts = ref([])

// 计算属性
const activeConnection = computed(() => connectionStatus.value.activeConnection)
const connections = computed(() => connectionStatus.value.connections)
const queuedMessages = computed(() => connectionStatus.value.queuedMessages)

const statusColor = computed(() => {
  if (activeConnection.value) return 'success'
  return 'warning'
})

const statusIcon = computed(() => {
  if (activeConnection.value) return 'mdi-check-circle'
  return 'mdi-alert-circle'
})

// 方法
const refreshStatus = async () => {
  loading.value = true
  try {
    const result = await ipc.invoke('connection:getStatus')
    if (result.success) {
      connectionStatus.value = result.data
    }
  } catch (error) {
    console.error('Failed to refresh connection status:', error)
  } finally {
    loading.value = false
  }
}

const switchConnection = async (type) => {
  switching.value = true
  try {
    const result = await ipc.invoke('connection:forceSwitch', { type })
    if (result.success) {
      await refreshStatus()
      // 显示成功通知
      await ipc.invoke('message:sendNotification', {
        type: 'success',
        content: `已切换到 ${getConnectionDisplayName(type)} 连接`,
        mode: 'snackbar',
        duration: 3000
      })
    } else {
      // 显示错误通知
      await ipc.invoke('message:sendNotification', {
        type: 'error',
        content: `切换到 ${getConnectionDisplayName(type)} 失败: ${result.error}`,
        mode: 'snackbar',
        duration: 5000
      })
    }
  } catch (error) {
    console.error('Failed to switch connection:', error)
    await ipc.invoke('message:sendNotification', {
      type: 'error',
      content: `连接切换失败: ${error.message}`,
      mode: 'snackbar',
      duration: 5000
    })
  } finally {
    switching.value = false
  }
}

const restartService = async () => {
  restarting.value = true
  try {
    const result = await ipc.invoke('connection:restartService')
    if (result.success) {
      await refreshStatus()
      await ipc.invoke('message:sendNotification', {
        type: 'success',
        content: '通信服务重启成功',
        mode: 'snackbar',
        duration: 3000
      })
    } else {
      await ipc.invoke('message:sendNotification', {
        type: 'error',
        content: `服务重启失败: ${result.error}`,
        mode: 'snackbar',
        duration: 5000
      })
    }
  } catch (error) {
    console.error('Failed to restart service:', error)
    await ipc.invoke('message:sendNotification', {
      type: 'error',
      content: `服务重启失败: ${error.message}`,
      mode: 'snackbar',
      duration: 5000
    })
  } finally {
    restarting.value = false
  }
}

const checkHealth = async () => {
  checking.value = true
  try {
    const result = await ipc.invoke('connection:checkHealth')
    if (result.success) {
      const { isHealthy } = result.data
      await ipc.invoke('message:sendNotification', {
        type: isHealthy ? 'success' : 'warning',
        content: `健康检查结果: ${isHealthy ? '正常' : '异常'}`,
        mode: 'snackbar',
        duration: 3000
      })
      await refreshStatus()
    }
  } catch (error) {
    console.error('Failed to check health:', error)
    await ipc.invoke('message:sendNotification', {
      type: 'error',
      content: `健康检查失败: ${error.message}`,
      mode: 'snackbar',
      duration: 5000
    })
  } finally {
    checking.value = false
  }
}

const openUsbConfig = async () => {
  try {
    const result = await ipc.invoke('connection:getUsbConfig')
    if (result.success) {
      Object.assign(usbConfig, result.data)
    }
    usbConfigDialog.value = true
  } catch (error) {
    console.error('Failed to get USB config:', error)
  }
}

const saveUsbConfig = async () => {
  savingConfig.value = true
  try {
    const result = await ipc.invoke('connection:updateUsbConfig', usbConfig)
    if (result.success) {
      usbConfigDialog.value = false
      await ipc.invoke('message:sendNotification', {
        type: 'success',
        content: 'USB 配置保存成功',
        mode: 'snackbar',
        duration: 3000
      })
    } else {
      await ipc.invoke('message:sendNotification', {
        type: 'error',
        content: `配置保存失败: ${result.error}`,
        mode: 'snackbar',
        duration: 5000
      })
    }
  } catch (error) {
    console.error('Failed to save USB config:', error)
    await ipc.invoke('message:sendNotification', {
      type: 'error',
      content: `配置保存失败: ${error.message}`,
      mode: 'snackbar',
      duration: 5000
    })
  } finally {
    savingConfig.value = false
  }
}

const listSerialPorts = async () => {
  listingPorts.value = true
  try {
    const result = await ipc.invoke('connection:listSerialPorts')
    if (result.success) {
      availablePorts.value = result.data
    } else {
      await ipc.invoke('message:sendNotification', {
        type: 'error',
        content: `获取串口列表失败: ${result.error}`,
        mode: 'snackbar',
        duration: 5000
      })
    }
  } catch (error) {
    console.error('Failed to list serial ports:', error)
    await ipc.invoke('message:sendNotification', {
      type: 'error',
      content: `获取串口列表失败: ${error.message}`,
      mode: 'snackbar',
      duration: 5000
    })
  } finally {
    listingPorts.value = false
  }
}

const selectPort = (path) => {
  usbConfig.devicePath = path
}

// 工具方法
const getConnectionDisplayName = (type) => {
  const names = {
    websocket: 'WebSocket',
    usb: 'USB'
  }
  return names[type] || type
}

const getStateText = (state) => {
  const states = {
    connected: '已连接',
    connecting: '连接中',
    disconnected: '已断开',
    error: '错误'
  }
  return states[state] || '未知'
}

const getStateColor = (state) => {
  const colors = {
    connected: 'success',
    connecting: 'warning',
    disconnected: 'error',
    error: 'error'
  }
  return colors[state] || 'grey'
}

const getStateClass = (state) => {
  return `state-${state || 'disconnected'}`
}

// 生命周期
onMounted(() => {
  refreshStatus()

  // 监听连接状态变化
  ipc.on('connection-state-changed', (event, data) => {
    connectionStatus.value = data.connectionStatus
  })

  // 监听连接错误
  ipc.on('connection-error', (event, data) => {
    console.warn('Connection error:', data)
  })

  // 定期刷新状态
  const interval = setInterval(refreshStatus, 30000) // 每30秒刷新一次

  onUnmounted(() => {
    clearInterval(interval)
    ipc.removeAllListeners('connection-state-changed')
    ipc.removeAllListeners('connection-error')
  })
})
</script>

<style scoped lang="scss">
.connection-monitor {
  .connection-card {
    height: 100%;
  }

  .connection-info {
    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .label {
        font-weight: 500;
        color: rgb(var(--v-theme-on-surface-variant));
      }
    }
  }

  .state-connected {
    color: rgb(var(--v-theme-success));
    font-weight: 500;
  }

  .state-connecting {
    color: rgb(var(--v-theme-warning));
    font-weight: 500;
  }

  .state-disconnected {
    color: rgb(var(--v-theme-error));
    font-weight: 500;
  }

  .state-error {
    color: rgb(var(--v-theme-error));
    font-weight: 500;
  }

  .port-item {
    cursor: pointer;
    
    &:hover {
      background-color: rgba(var(--v-theme-primary), 0.1);
    }
  }
}
</style>
