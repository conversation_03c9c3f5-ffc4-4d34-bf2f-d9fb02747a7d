import { createI18n } from 'vue-i18n'

const messages = {
  en: {
    message: {
      hello: 'Hello World',
      home: 'MQ<PERSON>',
      profile: {
        title: 'Network',
        name: 'Name',
        age: 'Age',
        email: 'Email',
        phone: 'Phone',
        address: 'Address',
        save: 'Save'
      },
      language: 'Language'
    }
  },
  zh: {
    message: {
      hello: '你好世界',
      home: '数据展示',
      profile: {
        title: '网络设置',
        name: '姓名',
        age: '年龄',
        email: '邮箱',
        phone: '电话',
        address: '地址',
        save: '保存'
      },
      language: '语言'
    }
  },
  ja: {
    message: {
      hello: 'こんにちは世界',
      home: 'ホーム',
      profile: {
        title: '网络设置',
        name: '名前',
        age: '年齢',
        email: 'メール',
        phone: '電話番号',
        address: '住所',
        save: '保存'
      },
      language: '言語'
    }
  }
}

export const i18n = createI18n({
  legacy: false,
  locale: 'zh',
  fallbackLocale: 'en',
  messages
})