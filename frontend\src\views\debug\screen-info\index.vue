<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import BackButton from "@/components/BackButton/index.vue";

const windowWidth = ref(window.innerWidth);
const windowHeight = ref(window.innerHeight);

const updateWindowSize = () => {
  windowWidth.value = window.innerWidth;
  windowHeight.value = window.innerHeight;
};

const back = () => {
  window.history.back();
};

onMounted(() => {
  window.addEventListener("resize", updateWindowSize);
});

onUnmounted(() => {
  window.removeEventListener("resize", updateWindowSize);
});
</script>

<template>
  <v-container class="fill-height">
    <v-row justify="center" align="center">
      <v-col cols="12" sm="8" md="6">
        <v-card class="mx-auto pa-4" elevation="4">
          <v-card-title class="text-center text-h5 mb-4" @click="back">窗口尺寸信息</v-card-title>
          <v-card-text>
            <v-list>
              <v-list-item>
                <template v-slot:prepend>
                  <v-icon icon="mdi-arrow-left-right" color="primary"></v-icon>
                </template>
                <v-list-item-title>窗口宽度：{{ windowWidth }}px</v-list-item-title>
              </v-list-item>
              <v-list-item>
                <template v-slot:prepend>
                  <v-icon icon="mdi-arrow-up-down" color="primary"></v-icon>
                </template>
                <v-list-item-title>窗口高度：{{ windowHeight }}px</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <BackButton />
  </v-container>
</template>

<style scoped>
.fill-height {
  height: 100%;
}
</style>
