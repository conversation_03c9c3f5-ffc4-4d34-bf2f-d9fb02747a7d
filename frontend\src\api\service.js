/**
 * API service with helper functions for common HTTP methods
 */

import api from './config';

/**
 * Performs a GET request
 * @param {string} url - The endpoint URL
 * @param {Object} params - URL parameters
 * @param {Object} config - Additional axios config
 * @returns {Promise} - The API response
 */
export const get = (url, params = {}, config = {}) => {
  return api.get(url, { params, ...config });
};

/**
 * Performs a POST request
 * @param {string} url - The endpoint URL
 * @param {Object} data - The request payload
 * @param {Object} config - Additional axios config
 * @returns {Promise} - The API response
 */
export const post = (url, data = {}, config = {}) => {
  return api.post(url, data, config);
};

/**
 * Performs a PUT request
 * @param {string} url - The endpoint URL
 * @param {Object} data - The request payload
 * @param {Object} config - Additional axios config
 * @returns {Promise} - The API response
 */
export const put = (url, data = {}, config = {}) => {
  return api.put(url, data, config);
};

/**
 * Performs a PATCH request
 * @param {string} url - The endpoint URL
 * @param {Object} data - The request payload
 * @param {Object} config - Additional axios config
 * @returns {Promise} - The API response
 */
export const patch = (url, data = {}, config = {}) => {
  return api.patch(url, data, config);
};

/**
 * Performs a DELETE request
 * @param {string} url - The endpoint URL
 * @param {Object} config - Additional axios config
 * @returns {Promise} - The API response
 */
export const del = (url, config = {}) => {
  return api.delete(url, config);
};

/**
 * Uploads a file
 * @param {string} url - The endpoint URL
 * @param {FormData} formData - The form data with files
 * @param {Function} onProgress - Progress callback function
 * @param {Object} config - Additional axios config
 * @returns {Promise} - The API response
 */
export const upload = (url, formData, onProgress = null, config = {}) => {
  const uploadConfig = {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    ...config,
  };
  
  if (onProgress) {
    uploadConfig.onUploadProgress = (progressEvent) => {
      const percentCompleted = Math.round(
        (progressEvent.loaded * 100) / progressEvent.total
      );
      onProgress(percentCompleted);
    };
  }
  
  return api.post(url, formData, uploadConfig);
};

// Export all methods as a service object
const apiService = {
  get,
  post,
  put,
  patch,
  delete: del,
  upload,
};

export default apiService;