<template>
  <div class="home-container">
    <v-row class="fill-height" justify="center">
      <v-col
        v-for="item in menuItems"
        :key="item.path"
        cols="4"
        class="pa-2 flex justify-center items-center"
      >
        <v-card
          class="feature-card"
          elevation="2"
          @click="() => handleCardClick(item.path)"
          hover
          color="primary"
          variant="outlined"
        >
          <v-card-title class="text-center d-flex flex-column align-center justify-center gap-4">
            <v-icon size="48">{{ item.icon }}</v-icon>
            <span class="card-text">{{ item.title }}</span>
          </v-card-title>
        </v-card>
      </v-col>
    </v-row>
  </div>
</template>

<script setup>
import { useRouter } from "vue-router";
import { ref } from "vue";

const router = useRouter();

const menuItems = ref([
  { path: "/auxiliary", icon: "mdi-monitor-dashboard", title: "辅助画面" },
  { path: "/switch", icon: "mdi-speedometer", title: "操作灵敏度" },
  { path: "/intelligence", icon: "mdi-brain", title: "智能化功能" },
  { path: "/settings", icon: "mdi-cog", title: "通用设置" },
  { path: "/network", icon: "mdi-lan", title: "网络状态" },
  { path: "/normal", icon: "mdi-sync", title: "常态化运行" },
  { path: "/datashow", icon: "mdi-chart-line", title: "监控中心" },
  { path: "/show", icon: "mdi-chart-bar", title: "数据分析" },
  { path: "/hello", icon: "mdi-information", title: "欢迎" },
]);

const handleCardClick = (path) => {
  setTimeout(() => router.push(path), 300);
};

</script>

<style scoped>
.home-container {
  width: 1280px;
  height: 800px;
  margin: 0 auto;
  padding: 60px 40px;
  background: url("../../assets/image/skin-bg.gif") repeat;
  display: flex;
  align-items: center;
}

.feature-card {
  width: 290px;
  height: 150px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.feature-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
}

.text-center {
  width: 100%;
}

.card-text {
  font-size: 18px;
  font-weight: 500;
  margin-top: 12px;
  color: rgba(255, 255, 255, 0.8);
  letter-spacing: 0.5px;
}


</style>
