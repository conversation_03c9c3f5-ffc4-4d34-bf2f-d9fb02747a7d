{"name": "bux-aux", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host --port 8080", "build": "vite build --mode production", "preview": "vite preview"}, "dependencies": {"@cycjimmy/jsmpeg-player": "^6.1.2", "@mdi/font": "^7.4.47", "autoprefixer": "^10.4.16", "axios": "^1.9.0", "echarts": "^5.6.0", "lodash-es": "^4.17.21", "pinia": "^3.0.1", "postcss": "^8.4.31", "prettier": "^3.5.1", "simple-keyboard": "^3.8.36", "tailwindcss": "^3.3.5", "vue": "^3.5.13", "vue-i18n": "^11.1.1", "vue-router": "^4.5.0", "vuetify": "^3.8.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "sass": "^1.85.1", "terser": "^5.39.0", "vconsole": "^3.15.1", "vite": "^6.1.0", "vite-plugin-vuetify": "^2.1.0"}}