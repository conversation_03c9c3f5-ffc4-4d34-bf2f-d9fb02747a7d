"use strict";

const parseConfig = {
  head: [
    { key: "featureValue", label: "特征位固定值", type: "", len: 2, isBigEndian: false, isShow: true },
    { key: "protocolVersion", label: "传输协议版本", type: "", len: 1, isBigEndian: false, isShow: true },
    { key: "vehicleType", label: "车辆类型标识", type: "", len: 1, isBigEndian: false, isShow: true },
    { key: "headerLength", label: "头长度", type: "", len: 2, isBigEndian: false, isShow: true },
    { key: "targetAddress", label: "目标地址", type: "", len: 1, isBigEndian: false, isShow: true },
    { key: "sourceAddress", label: "源地址", type: "", len: 1, isBigEndian: false, isShow: true },
    { key: "transmitCount", label: "传输计数", type: "", len: 1, isBigEndian: false, isShow: true },
    { key: "dataUnitCount", label: "数据单元ID个数", type: "", len: 1, isBigEndian: false, isShow: true },
    { key: "dataUnitLength", label: "数据单元长度", type: "", len: 2, isBigEndian: false, isShow: true },
    { key: "timestamp", label: "时间戳(毫秒级)", type: "TIME", len: 8, isBigEndian: false, isShow: true },
    { key: "reserved", label: "保留", type: "", len: 0, isBigEndian: false, isShow: true },
  ],
  ID8: [
    { key: "id", label: "a2r8", type: "BIN", len: 2, isBigEndian: false, isShow: true },
    { key: "dataLength", label: "数据长度", type: "BIN", len: 2, isBigEndian: false, isShow: true },
    { key: "emergencyBrake", label: "紧急制动", type: "BIN", len: 1, isBigEndian: false, isShow: true },
    { key: "consoleEmergencyStop", label: "操作台急停", type: "BIN", len: 1, isBigEndian: false, isShow: true },
    { key: "reserved1", label: "保留", type: "BIN", len: 1, isBigEndian: false, isShow: true },
    { key: "reserved2", label: "保留", type: "BIN", len: 1, isBigEndian: false, isShow: true },
    { key: "leftJoystickX", label: "左手柄X轴", type: "FLOAT", len: 4, isBigEndian: false, isShow: true },
    { key: "leftJoystickY", label: "左手柄Y轴", type: "FLOAT", len: 4, isBigEndian: false, isShow: true },
    { key: "leftJoystickZ", label: "左手柄Z轴或KX轴", type: "FLOAT", len: 4, isBigEndian: false, isShow: true },
    { key: "leftJoystickKY", label: "左手柄KY轴", type: "FLOAT", len: 4, isBigEndian: false, isShow: true },
    { key: "leftJoystickButtons", label: "左手柄按钮", type: "BIN", len: 2, isBigEndian: false, isShow: true },
    { key: "rightJoystickX", label: "右手柄X轴", type: "FLOAT", len: 4, isBigEndian: false, isShow: true },
    { key: "rightJoystickY", label: "右手柄Y轴", type: "FLOAT", len: 4, isBigEndian: false, isShow: true },
    { key: "rightJoystickZ", label: "右手柄Z轴或KX轴", type: "FLOAT", len: 4, isBigEndian: false, isShow: true },
    { key: "rightJoystickKY", label: "右手柄KY轴", type: "FLOAT", len: 4, isBigEndian: false, isShow: true },
    { key: "rightJoystickButtons", label: "右手柄按钮", type: "BIN", len: 2, isBigEndian: false, isShow: true },
    { key: "leftPedal", label: "左踏板", type: "FLOAT", len: 4, isBigEndian: false, isShow: true },
    { key: "rightPedal", label: "右踏板", type: "FLOAT", len: 4, isBigEndian: false, isShow: true },
    { key: "functionButtons", label: "功能按钮", type: "BIN", len: 8, isBigEndian: false, isShow: true },
    { key: "simulatedLeftPedal", label: "模拟左脚踏", type: "FLOAT", len: 4, isBigEndian: false, isShow: true },
    { key: "simulatedRightPedal", label: "模拟右脚踏", type: "FLOAT", len: 4, isBigEndian: false, isShow: true },
  ],
  ID16384: [
    { key: "id", label: "r2a16384", type: "BIN", len: 2, isBigEndian: false, isShow: true },
    { key: "dataLength", label: "数据长度", type: "BIN", len: 2, isBigEndian: false, isShow: true },
    { key: "emergencyBrakeStatus", label: "紧急制动状态", type: "BIN", len: 1, isBigEndian: false, isShow: true },
    { key: "consoleStopStatus", label: "操作台急停状态", type: "BIN", len: 1, isBigEndian: false, isShow: true },
    { key: "remoteControlStatus", label: "遥控使能状态", type: "BIN", len: 1, isBigEndian: false, isShow: true },
    { key: "localStopStatus", label: "本地急停状态", type: "BIN", len: 1, isBigEndian: false, isShow: true },
    { key: "functionButtonStatus", label: "功能按钮状态", type: "BIN", len: 4, isBigEndian: false, isShow: true },
    { key: "engineSpeed", label: "发动机转速", type: "FLOAT", len: 4, isBigEndian: false, isShow: true },
    { key: "fuelLevel", label: "燃油液位", type: "FLOAT", len: 4, isBigEndian: false, isShow: true },
    { key: "oilPressure", label: "机油压力", type: "FLOAT", len: 4, isBigEndian: false, isShow: true },
    { key: "batteryVoltage", label: "电池电压", type: "FLOAT", len: 4, isBigEndian: false, isShow: true },
    { key: "waterTemperature", label: "水温", type: "FLOAT", len: 4, isBigEndian: false, isShow: true },
    { key: "oilTemperature", label: "油温", type: "FLOAT", len: 4, isBigEndian: false, isShow: true },
    { key: "fuelPressure", label: "燃油输送压力", type: "FLOAT", len: 4, isBigEndian: false, isShow: true },
    { key: "coolantLevel", label: "冷却剂液位", type: "FLOAT", len: 4, isBigEndian: false, isShow: true },
    { key: "cabinTemperature", label: "驾驶室温度", type: "FLOAT", len: 4, isBigEndian: false, isShow: true },
    { key: "ambientTemperature", label: "环境气温", type: "FLOAT", len: 4, isBigEndian: false, isShow: true },
    { key: "hydraulicOilTemperature", label: "液压油温度", type: "FLOAT", len: 4, isBigEndian: false, isShow: true },
  ],
  ID11: [
    { key: "id", label: "11接收心跳", type: "", len: 2, isBigEndian: false, isShow: true },
    { key: "dataLength", label: "数据长度", type: "", len: 2, isBigEndian: false, isShow: true },
    { key: "senderStatus", label: "发送者当前状态", type: "", len: 1, isBigEndian: false, isShow: true },
    { key: "otherInfo", label: "其他信息", type: "", len: 3, isBigEndian: false, isShow: true },
  ],
  ID20: [
    { key: "id", label: "20接收数据", type: "BIN", len: 2, isBigEndian: false, isShow: true },
    { key: "dataLength", label: "数据长度", type: "BIN", len: 2, isBigEndian: false, isShow: true },
    { key: "communicationType", label: "参数通讯类型", type: "BIN", len: 1, isBigEndian: false, isShow: true },
    { key: "parameterPackageType", label: "参数包类型", type: "BIN", len: 2, isBigEndian: false, isShow: true },
    { key: "reserved", label: "预留", type: "BIN", len: 1, isBigEndian: false, isShow: true },
    { key: "jsonData", label: "JSON数据", type: "BIN", len: 0, isBigEndian: false, isShow: true }
  ],
  ID2048: [
    { key: "id", label: "2048车辆反馈", type: "BIN", len: 2, isBigEndian: false, isShow: true },
    { key: "dataLength", label: "数据长度", type: "BIN", len: 2, isBigEndian: false, isShow: true },
    { key: "vehicleSubType", label: "子车辆类型", type: "BIN", len: 2, isBigEndian: false, isShow: true },
    { key: "senderStatus", label: "发送者自身状态", type: "BIN", len: 1, isBigEndian: false, isShow: true },
    { key: "vehicleKeyStatus", label: "车端关键状态", type: "BIN", len: 1, isBigEndian: false, isShow: true },
    { key: "consoleKeyStatus", label: "操作台端关键状态", type: "BIN", len: 1, isBigEndian: false, isShow: true },
    { key: "failureLevel", label: "失效等级", type: "BIN", len: 1, isBigEndian: false, isShow: true },
    { key: "failureSource", label: "失效来源", type: "BIN", len: 1, isBigEndian: false, isShow: true },
    { key: "failureType", label: "失效类型", type: "BIN", len: 1, isBigEndian: false, isShow: true },
    { key: "leftJoystickButtons", label: "左手柄按钮", type: "BIN", len: 2, isBigEndian: false, isShow: true },
    { key: "rightJoystickButtons", label: "右手柄按钮", type: "BIN", len: 2, isBigEndian: false, isShow: true },
    { key: "customButtons", label: "自定义按钮", type: "BIN", len: 8, isBigEndian: false, isShow: true },
    { key: "safetyButtons", label: "安全按钮", type: "BIN", len: 2, isBigEndian: false, isShow: true },
    { key: "extendedSafetyFeatures", label: "扩展安全功能", type: "BIN", len: 2, isBigEndian: false, isShow: true },
    { key: "extendedGeneralFeatures", label: "扩展普通功能", type: "BIN", len: 24, isBigEndian: false, isShow: true }
  ]
};

const encoderConfig = {
  ID12: [
    { key: "id", label: "12回复心跳", value: 12, len: 2, isBigEndian: false, isShow: true },
    { key: "dataLength", label: "LEN=12", value: 12, len: 2, isBigEndian: false, isShow: true },
    { key: "senderStatus", label: "发送者当前状态", value: 32, len: 1, isBigEndian: false, isShow: true },
    { key: "otherInfo", label: "其他信息", value: 0, len: 3, isBigEndian: false, isShow: true },
    { key: "confirmedId", label: "被确认的ID预留", value: 0, len: 2, isBigEndian: false, isShow: true },
    { key: "confirmedTransmitCount", label: "被确认的传输计数", value: 0, len: 1, isBigEndian: false, isShow: true },
    { key: "executeResult", label: "执行结果", value: 0, len: 1, isBigEndian: false, isShow: true },
  ],
  ID20: [
    { key: "id", label: "20接收数据", value: 20, type: "BIN", len: 2, isBigEndian: false, isShow: true },
    { key: "dataLength", label: "数据长度", value: 8, type: "BIN", len: 2, isBigEndian: false, isShow: true },
    { key: "communicationType", label: "参数通讯类型", value: 2, type: "BIN", len: 1, isBigEndian: false, isShow: true },
    { key: "parameterPackageType", label: "参数包类型", value: 0, type: "BIN", len: 2, isBigEndian: false, isShow: true },
    { key: "reserved", label: "预留", value: 0, type: "BIN", len: 1, isBigEndian: false, isShow: true },
    { key: "jsonData", label: "JSON数据", value: {}, type: "BIN", len: 99, isBigEndian: false, isShow: true }
  ],
  ID1100: [
    { key: "id", label: "数据内容", value: 1100, type: "BIN", len: 2, isBigEndian: false, isShow: true },
    { key: "dataLength", label: "数据长度", value: 36, type: "BIN", len: 2, isBigEndian: false, isShow: true },
    { key: "vehicleSubType", label: "子车辆类型", value: 0, type: "BIN", len: 2, isBigEndian: false, isShow: true },
    { key: "tabletStatus", label: "平板电脑状态", value: 0, type: "BIN", len: 1, isBigEndian: false, isShow: true },
    { key: "failureLevel", label: "失效等级", value: 255, type: "BIN", len: 1, isBigEndian: false, isShow: true },
    { key: "failureSource", label: "失效来源", value: 0, type: "BIN", len: 1, isBigEndian: false, isShow: true },
    { key: "failureType", label: "失效类型", value: 0, type: "BIN", len: 1, isBigEndian: false, isShow: true },
    { key: "extendedSafetyFeatures", label: "扩展安全功能", value: 0, type: "BIN", len: 2, isBigEndian: false, isShow: true },
    { key: "extendedGeneralFeatures", label: "扩展普通功能", value: 0, type: "BIN", len: 24, isBigEndian: false, isShow: true }
  ]
};

module.exports = { parseConfig, encoderConfig };
