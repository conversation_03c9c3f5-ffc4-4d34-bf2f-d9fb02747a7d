"use strict";

const mqtt = require("mqtt");
const { logger } = require("ee-core/log");
const { getMainWindow } = require("ee-core/electron");
const MessageParser = require("../utils/messageParser");

function throttle(func, wait) {
  let lastTime = 0;
  let pendingArgs = null;
  let timeoutId = null;

  return function (...args) {
    const now = Date.now();
    pendingArgs = args;

    if (now - lastTime >= wait) {
      func.apply(this, pendingArgs);
      lastTime = now;
      pendingArgs = null;
    } else if (!timeoutId) {
      timeoutId = setTimeout(() => {
        if (pendingArgs) {
          func.apply(this, pendingArgs);
          lastTime = Date.now();
        }
        timeoutId = null;
        pendingArgs = null;
      }, wait - (now - lastTime));
    }
  };
}

class MqttService {
  constructor() {
    this.client = null;
    this.throttleSendToRender = throttle(this.sendToRender.bind(this), 300);
  }

  // 连接到MQTT代理
  connect(config) {
    // 连接配置
    const mqttOptions = {
      username: config.username,
      password: config.password,
      clientId: "electron-client-" + Math.random().toString(16).substr(2, 8),
      clean: true,
      connectTimeout: 4000,
      reconnectPeriod: 1000,
    };
    console.log(mqttOptions);

    const brokerUrl = config.brokerUrl;
    this.client = mqtt.connect(brokerUrl, mqttOptions);

    // 连接成功事件
    this.client.on("connect", () => {
      logger.info("[mqttService] Connected to MQTT broker");
    });

    // 连接错误事件
    this.client.on("error", (err) => {
      logger.error("[mqttService] MQTT connection error:", err);
    });

    // 连接关闭事件
    this.client.on("close", () => {
      logger.info("[mqttService] MQTT connection closed");
    });

    // 重新连接事件
    this.client.on("reconnect", () => {
      logger.info("[mqttService] Reconnecting to MQTT broker");
    });

    // 消息接收事件
    this.client.on("message", (topic, message) => {
      // logger.info(`[mqttService]topic: ${topic} Received message: ${message}`);
      // 处理接收到的消息
      this.handleMessage(topic, message);
    });
  }

  // 断开与MQTT代理的连接
  disconnect() {
    if (this.client) {
      this.client.end();
      logger.info("[mqttService] Disconnected from MQTT broker");
    }
  }

  // 发送消息到指定主题
  sendMessage(topic, message) {
    if (this.client) {
      this.client.publish(topic, message, (err) => {
        if (err) {
          logger.error("[mqttService] Failed to send message:", err);
        } else {
          logger.info(`[mqttService] Message sent to topic: ${topic}`);
        }
      });
    }
  }

  sendToRender(data) {
    if (!getMainWindow().isDestroyed()) {
      getMainWindow().webContents.send("mqtt-message", data);
    }
  }

  // 订阅指定主题
  subscribe(topic) {
    if (this.client) {
      this.client.subscribe(topic, (err) => {
        if (err) {
          logger.error(`[mqttService] Failed to subscribe to topic: ${topic}`, err);
        } else {
          logger.info(`[mqttService] Subscribed to topic: ${topic}`);
        }
      });
    }
  }

  // 取消订阅指定主题
  unsubscribe(topic) {
    if (this.client) {
      this.client.unsubscribe(topic, (err) => {
        if (err) logger.error(`[mqttService] Failed to unsubscribe from topic: ${topic}`, err);
      });
    }
  }

  // 处理接收到的消息
  handleMessage(topic, message) {
    let res = MessageParser.parse(message, "mqtt");
    if (res && Object.keys(res).length > 20) {
      // console.log("message", res);
      this.throttleSendToRender(res);
    }
  }
  isConnected() {
    return this.client && this.client.connected;
  }
}

module.exports = new MqttService();
