<template>
  <v-container>
    <v-row>
      <!-- 网络监控控制卡片 -->
      <v-col cols="6" class="my-0 pt-1">
        <v-card height="100%">
          <v-card-title class="d-flex align-center">
            <span>网络监控</span>
            <v-spacer></v-spacer>
            <v-chip :color="isMonitoring ? 'success' : 'error'" class="ml-2">
              {{ isMonitoring ? "监控中" : "未监控" }}
            </v-chip>
          </v-card-title>
          <v-card-text>
            <v-btn :color="isMonitoring ? 'error' : 'success'" :loading="loading" block @click="toggleMonitoring">
              {{ isMonitoring ? "停止监控" : "开始监控" }}
            </v-btn>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- 手动检测卡片 -->
      <v-col cols="6" class="my-0 pt-1">
        <v-card height="100%">
          <v-card-title>手动检测</v-card-title>
          <v-card-text class="d-flex align-center">
            <v-btn color="primary" :loading="checking" block @click="manualCheck"> 检测连通性 </v-btn>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- 连通性状态卡片 -->
      <v-col cols="12" class="my-0 pt-1 h-[174px]">
        <v-card>
          <v-card-title class="d-flex align-center">
            <span>连通性状态</span>
            <v-spacer></v-spacer>
            <v-chip v-if="lastCheckTime" class="ml-2" size="small">{{ lastCheckTime }}</v-chip>
          </v-card-title>
          <v-card-text>
            <v-row>
              <v-col v-for="(item, key) in networkStatus" :key="key" cols="4">
                <v-card variant="outlined">
                  <v-card-title class="d-flex align-center py-2">
                    <span class="text-subtitle-1">{{ getServiceName(key) }}</span>
                    <v-spacer></v-spacer>
                    <v-chip :color="item.isConnected ? 'success' : 'error'" size="small" class="ml-2">
                      {{ item.isConnected ? "连接正常" : "连接失败" }}
                    </v-chip>
                  </v-card-title>
                  <v-card-text class="py-1">
                    <div class="d-flex justify-space-between">
                      <span class="text-caption">URL:</span>
                      <span class="text-caption">{{ item.url }}</span>
                    </div>
                    <div class="d-flex justify-space-between">
                      <span class="text-caption">延迟:</span>
                      <span class="text-caption">
                        {{ item.latency > 0 ? `${item.latency}ms` : "--" }}
                      </span>
                    </div>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- 历史数据卡片 -->
      <v-col cols="12" class="my-0 pt-1">
        <v-card>
          <v-card-title class="d-flex align-center">
            <span>历史数据</span>
            <v-spacer></v-spacer>
            <v-btn color="primary" size="small" variant="text" class="mr-2" @click="fetchHistoryData(1)">
              刷新数据
            </v-btn>
            <v-btn color="error" size="small" variant="text" @click="clearHistory" :disabled="!historyData.length">
              清空记录
            </v-btn>
          </v-card-title>
          <v-card-text>
            <div class="d-flex align-center mb-3 justify-space-between">
              <div>
                <span class="text-body-2 mr-2">共 {{ totalItems }} 条记录</span>
                <span class="text-body-2">第 {{ currentPage }}/{{ totalPages || 1 }} 页</span>
              </div>
              <div>
                <v-pagination
                  v-model="currentPage"
                  :length="totalPages || 1"
                  :total-visible="5"
                  @update:model-value="fetchHistoryData"
                  density="compact"
                ></v-pagination>
              </div>
            </div>

            <div v-if="historyData.length">
              <v-table density="compact" height="360px" fixed-header>
                <thead>
                  <tr>
                    <th class="text-left">时间</th>
                    <th class="text-left">Android</th>
                    <th class="text-left">服务器</th>
                    <th class="text-left">ROS</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(item, index) in historyData" :key="index">
                    <td>{{ formatTime(item.timestamp) }}</td>
                    <td>
                      <v-chip
                        :color="item.results.android?.isConnected ? 'success' : 'error'"
                        size="x-small"
                        class="mr-1"
                      >
                        {{ item.results.android?.isConnected ? "✓" : "✗" }}
                      </v-chip>
                      {{ item.results.android?.latency > 0 ? `${item.results.android.latency}ms` : "--" }}
                    </td>
                    <td>
                      <v-chip
                        :color="item.results.server?.isConnected ? 'success' : 'error'"
                        size="x-small"
                        class="mr-1"
                      >
                        {{ item.results.server?.isConnected ? "✓" : "✗" }}
                      </v-chip>
                      {{ item.results.server?.latency > 0 ? `${item.results.server.latency}ms` : "--" }}
                    </td>
                    <td>
                      <v-chip :color="item.results.ros?.isConnected ? 'success' : 'error'" size="x-small" class="mr-1">
                        {{ item.results.ros?.isConnected ? "✓" : "✗" }}
                      </v-chip>
                      {{ item.results.ros?.latency > 0 ? `${item.results.ros.latency}ms` : "--" }}
                    </td>
                  </tr>
                </tbody>
              </v-table>
            </div>
            <div v-else class="text-center py-4">
              <p class="text-subtitle-1">暂无历史数据</p>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <BackButton />
  </v-container>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, onBeforeUnmount, computed } from "vue";
import BackButton from "@/components/BackButton/index.vue";
import { useRouter } from "vue-router";
import { ipc } from "@/utils/ipcRenderer";

const router = useRouter();

// 状态变量
const isMonitoring = ref(false);
const loading = ref(false);
const checking = ref(false);
const networkStatus = ref({});
const lastCheckTime = ref("");

// 历史数据相关
const historyData = ref([]);
const currentPage = ref(1);
const itemsPerPage = 100;
const totalPages = ref(0);
const totalItems = ref(0);

// 开始/停止网络监控
async function toggleMonitoring() {
  loading.value = true;
  try {
    if (!isMonitoring.value) {
      await ipc.invoke("controller/network/startMonitoring", { interval: 5000 });
      isMonitoring.value = true;
    } else {
      await ipc.invoke("controller/network/stopMonitoring");
      isMonitoring.value = false;
    }
  } catch (error) {
    console.error("Network monitoring error:", error);
  } finally {
    loading.value = false;
  }
}

// 手动检测连通性
async function manualCheck() {
  checking.value = true;
  try {
    await ipc.invoke("controller/network/checkConnectivity");
  } catch (error) {
    console.error("Manual check error:", error);
  } finally {
    checking.value = false;
  }
}

// 获取历史数据
async function fetchHistoryData(page = 1) {
  try {
    // 从localStorage获取数据
    const storageKey = "network_monitoring_data";
    const storedData = localStorage.getItem(storageKey);

    if (!storedData) {
      historyData.value = [];
      totalPages.value = 0;
      totalItems.value = 0;
      return;
    }

    // 解析存储的数据
    const allData = JSON.parse(storedData);

    // 按时间排序（最新的在前面）
    allData.sort((a, b) => {
      return new Date(b.timestamp) - new Date(a.timestamp);
    });

    // 计算总页数和总条目数
    totalItems.value = allData.length;
    totalPages.value = Math.ceil(allData.length / itemsPerPage);

    // 获取当前页的数据
    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    historyData.value = allData.slice(startIndex, endIndex);

    // 更新当前页码
    currentPage.value = page;
  } catch (error) {
    console.error("Failed to fetch history data:", error);
    historyData.value = [];
    totalPages.value = 0;
    totalItems.value = 0;
  }
}

// 清空历史数据
async function clearHistory() {
  try {
    localStorage.removeItem("network_monitoring_data");
    historyData.value = [];
  } catch (error) {
    console.error("Failed to clear history:", error);
  }
}

// 格式化时间
function formatTime(timestamp) {
  const date = new Date(timestamp);
  return `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;
}

// 获取服务名称
function getServiceName(key) {
  const nameMap = {
    android: "Android",
    server: "服务器",
    ros: "ROS",
  };
  return nameMap[key] || key;
}

// 处理网络状态更新
function handleNetworkStatusUpdate(event, data) {
  networkStatus.value = data.results;
  lastCheckTime.value = new Date(data.timestamp).toLocaleTimeString();

  // 存储数据到localStorage
  saveNetworkData(data);
}

// 保存网络监控数据到localStorage
function saveNetworkData(data) {
  try {
    const storageKey = "network_monitoring_data";

    // 获取已有数据
    let storedData = localStorage.getItem(storageKey);
    let dataArray = [];

    if (storedData) {
      dataArray = JSON.parse(storedData);
    }

    // 添加新数据
    dataArray.push(data);

    // 限制存储数量，保留最近3000条记录
    if (dataArray.length > 3000) {
      dataArray = dataArray.slice(-3000);
    }

    // 保存回localStorage
    localStorage.setItem(storageKey, JSON.stringify(dataArray));

    // 更新总条目数和总页数
    totalItems.value = dataArray.length;
    totalPages.value = Math.ceil(dataArray.length / itemsPerPage);
  } catch (error) {
    console.error("Failed to save network data:", error);
  }
}

// 组件挂载时
onMounted(async () => {
  // 添加网络状态更新监听器
  ipc.on("network-status-update", handleNetworkStatusUpdate);

  // 获取当前监控状态
  try {
    const status = await ipc.invoke("controller/network/getStatus");
    isMonitoring.value = status.isMonitoring;
    if (status.latestResults && Object.keys(status.latestResults).length > 0) {
      networkStatus.value = status.latestResults;
      lastCheckTime.value = new Date().toLocaleTimeString();
    }
  } catch (error) {
    console.error("Failed to get network status:", error);
  }

  // 获取历史数据
  fetchHistoryData(1);
});

// 组件卸载时
onUnmounted(() => {
  ipc.removeListener("network-status-update", handleNetworkStatusUpdate);
});

// 组件卸载前
onBeforeUnmount(() => {
  // 如果正在监控，则停止监控
  // if (isMonitoring.value) {
  //   ipc.invoke("controller/network/stopMonitoring").catch((error) => {
  //     console.error("Failed to stop monitoring:", error);
  //   });
  // }
});
</script>

<style scoped>
.text-wrap {
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
