/**
 * USB消息解析器简单测试
 * 验证USB协议的基本功能
 */

const MessageParser = require('./messageParser');

/**
 * 测试USB协议的基本功能
 */
function testUsbBasicFunctions() {
  console.log("=== USB基本功能测试开始 ===\n");

  // 测试1: 完整的消息解析流程
  console.log("测试1: 完整的消息解析流程");
  
  // 创建一个符合协议的完整消息
  const originalMessage = new Uint8Array([
    // 消息头 (20字节)
    0x0C, 0x10,       // 特征位
    0x30,             // 传输协议版本
    0x04,             // 车辆类型标识
    0x14, 0x00,       // 头长度 (20)
    0x81,             // 目标地址
    0x02,             // 源地址
    0x00,             // 传输计数
    0x01,             // 数据单元ID个数
    0x0C, 0x00,       // 数据单元长度 (12)
    0x50, 0x30, 0xFC, 0xAF, 0x7F, 0x60, 0x95, 0x01, // 时间戳
    
    // 消息体 (12字节)
    0x0B, 0x00,       // ID (11)
    0x0C, 0x00,       // 数据长度
    0x08,             // 发送方状态
    0x00,             // 通信类型
    0x01, 0x02, 0x03, // 其他信息
    0x00,             // 保留字段
    0xAA, 0xAA        // 扩展安全功能
  ]);

  console.log(`原始消息长度: ${originalMessage.length} 字节`);

  // 封装为USB消息
  const usbPacket = MessageParser.packUsbMessage(originalMessage);
  if (!usbPacket) {
    console.error("封装失败!");
    return;
  }
  console.log(`USB包长度: ${usbPacket.length} 字节`);

  // 解析USB消息
  const parsedResult = MessageParser.parse(usbPacket, "usb");
  
  if (parsedResult) {
    console.log("解析成功!");
    console.log(`消息ID: ${parsedResult.id || 'N/A'}`);
    console.log(`发送方状态: ${parsedResult.senderStatus || 'N/A'}`);
    console.log(`通信类型: ${parsedResult.communicationType || 'N/A'}`);
    
    // 验证关键字段
    const isValid = parsedResult.id === 11 &&
                   parsedResult.senderStatus === 8;
    console.log(`数据完整性验证: ${isValid ? '通过' : '失败'}`);
  } else {
    console.error("解析失败!");
  }

  console.log("\n=== USB基本功能测试结束 ===");
}

/**
 * 测试USB协议的错误处理
 */
function testUsbErrorHandling() {
  console.log("\n=== USB错误处理测试开始 ===\n");

  // 测试1: 无效的起始符
  console.log("测试1: 无效的起始符");
  const invalidStart = new Uint8Array([0xFF, 0x01, 0x02, 0x03, 0x7D]);
  const result1 = MessageParser.parseUsbMessage(invalidStart);
  console.log(`结果: ${result1 ? '意外成功' : '正确失败'}`);

  // 测试2: 缺少终止符
  console.log("\n测试2: 缺少终止符");
  const missingEnd = new Uint8Array([0x7E, 0x01, 0x02, 0x03]);
  const result2 = MessageParser.parseUsbMessage(missingEnd);
  console.log(`结果: ${result2 ? '意外成功' : '正确失败'}`);

  // 测试3: 数据太短
  console.log("\n测试3: 数据太短");
  const tooShort = new Uint8Array([0x7E, 0x7D]);
  const result3 = MessageParser.parseUsbMessage(tooShort);
  console.log(`结果: ${result3 ? '意外成功' : '正确失败'}`);

  console.log("\n=== USB错误处理测试结束 ===");
}

/**
 * 测试数据转义功能
 */
function testUsbEscaping() {
  console.log("\n=== USB转义功能测试开始 ===\n");

  // 测试包含需要转义字符的数据
  const testData = new Uint8Array([0x7E, 0x7D, 0x7C, 0x01, 0x02, 0x03]);
  console.log(`原始数据: ${Array.from(testData).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' ')}`);
  
  const escaped = MessageParser.escapeUsbData(testData);
  console.log(`转义后: ${Array.from(escaped).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' ')}`);
  
  const unescaped = MessageParser.unescapeUsbData(escaped);
  console.log(`反转义后: ${Array.from(unescaped).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' ')}`);
  
  const isEqual = testData.length === unescaped.length && 
                  testData.every((val, index) => val === unescaped[index]);
  console.log(`转义/反转义测试: ${isEqual ? '通过' : '失败'}`);

  console.log("\n=== USB转义功能测试结束 ===");
}

/**
 * 测试CRC计算
 */
function testUsbCrc() {
  console.log("\n=== USB CRC测试开始 ===\n");

  const testData = new Uint8Array([0x0C, 0x10, 0x30, 0x04, 0x14, 0x00]);
  const crc = MessageParser.calculateCrc16(testData);
  console.log(`测试数据: ${Array.from(testData).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' ')}`);
  console.log(`CRC-16: 0x${crc.toString(16).padStart(4, '0')}`);

  // 验证CRC计算的一致性
  const crc2 = MessageParser.calculateCrc16(testData);
  console.log(`CRC一致性测试: ${crc === crc2 ? '通过' : '失败'}`);

  console.log("\n=== USB CRC测试结束 ===");
}

// 运行所有测试
if (require.main === module) {
  testUsbBasicFunctions();
  testUsbErrorHandling();
  testUsbEscaping();
  testUsbCrc();
}

module.exports = {
  testUsbBasicFunctions,
  testUsbErrorHandling,
  testUsbEscaping,
  testUsbCrc
};
