'use strict';

const LogService = require('../service/logService');

class LogController {
  constructor() {
    this.logService = new LogService();
  }

  /**
   * 读取日志文件内容
   * @param {object} args - 参数对象
   * @param {string} args.logName - 日志文件名
   * @returns {Promise<string>} 日志内容
   */
  async read(args) {
    const { logName } = args;
    return await this.logService.readLog(logName);
  }

  /**
   * 获取日志文件列表
   * @param {object} args - 参数对象
   * @param {Date} [args.date] - 日期筛选
   * @returns {Promise<Array<Object>>} 日志文件列表
   */
  async getFiles(args = {}) {
    const options = {};
    if (args.date) {
      options.date = new Date(args.date);
    }
    return await this.logService.getLogFiles(options);
  }

  /**
   * 分页读取日志文件内容
   * @param {object} args - 参数对象
   * @param {string} args.logName - 日志文件名
   * @param {number} args.page - 页码
   * @returns {Promise<Object>} 日志内容和分页信息
   */
  async readByPage(args) {
    const { logName, page } = args;
    return await this.logService.readLogByPage(logName, page);
  }

  /**
   * 监听日志文件变化
   * @param {object} args - 参数对象
   * @param {string} args.logName - 日志文件名
   * @param {Function} args.callback - 回调函数
   * @returns {Function} 取消监听的函数
   */
  async watch(args) {
    const { logName } = args;
    return this.logService.watchLog(logName, (content) => {
      args.callback(content);
    });
  }

  /**
   * 清理指定天数之前的日志文件
   * @param {object} args - 参数对象
   * @param {number} [args.days=30] - 清理多少天之前的日志，默认30天
   * @returns {Promise<Object>} 清理结果
   */
  async cleanup(args = {}) {
    const days = args.days || 30;
    return await this.logService.cleanupOldLogs(days);
  }

  /**
   * 启动定时清理日志任务
   * @param {object} args - 参数对象
   * @param {number} [args.days=30] - 清理多少天之前的日志，默认30天
   * @param {number} [args.interval=86400000] - 清理间隔，默认24小时（单位：毫秒）
   * @returns {boolean} 是否成功启动
   */
  async startAutoCleanup(args = {}) {
    const days = args.days || 30;
    const interval = args.interval || 24 * 60 * 60 * 1000; // 默认24小时
    this.logService.startAutoCleanup(days, interval);
    return true;
  }

  /**
   * 停止定时清理日志任务
   * @returns {boolean} 是否成功停止
   */
  async stopAutoCleanup() {
    this.logService.stopAutoCleanup();
    return true;
  }
}

module.exports = LogController;