<template>
  <v-container>
    <v-row>
      <!-- MQTT连接控制卡片 -->
      <v-col cols="6" class="my-0 pt-1">
        <v-card height="100%">
          <v-card-title class="d-flex align-center">
            <span>MQTT连接</span>
            <v-spacer></v-spacer>
            <v-chip :color="connectionStatus ? 'success' : 'error'" class="ml-2">
              {{ connectionStatus ? "已连接" : "未连接" }}
            </v-chip>
          </v-card-title>
          <v-card-text>
            <v-btn
              :color="connectionStatus ? 'error' : 'success'"
              :loading="connecting"
              block
              @click="toggleConnection"
            >
              {{ connectionStatus ? "断开连接" : "连接" }}
            </v-btn>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- 消息订阅卡片 -->
      <v-col cols="6" class="my-0 pt-1">
        <v-card height="100%">
          <v-card-text class="d-flex align-center gap-4">
            <v-select
              v-model="topic"
              label="订阅主题"
              :items="[
                { title: 'Keep Live', value: mqttSubscription.mqttKeepLive },
                { title: 'To Android', value: mqttSubscription.mqttToAndroid },
                { title: 'To Excavator', value: mqttSubscription.mqttToExcavator },
              ]"
              :disabled="!connectionStatus"
              hide-details
              class="flex-grow-1"
            ></v-select>
            <v-btn
              :color="subscribed ? 'error' : 'success'"
              :disabled="!connectionStatus || !topic"
              min-width="100"
              @click="toggleSubscription"
            >
              {{ subscribed ? "取消订阅" : "订阅" }}
            </v-btn>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- 最新消息卡片 -->
      <v-col cols="12" class="my-0 pt-0">
        <v-card v-if="latestMessage">
          <v-card-title class="d-flex align-center">
            <span>最新消息</span>
            <v-spacer></v-spacer>
            <v-chip class="ml-2" size="small">{{ latestMessage.timestamp }}</v-chip>
          </v-card-title>
          <v-card-text>
            <v-table density="compact">
              <thead>
                <tr>
                  <th class="text-left" width="25%">键名</th>
                  <th class="text-left" width="25%">值</th>
                  <th class="text-left" width="25%">键名</th>
                  <th class="text-left" width="25%">值</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(chunk, index) in messageChunks" :key="index">
                  <template v-for="(item, itemIndex) in chunk" :key="itemIndex">
                    <td class="text-subtitle-2 font-weight-medium">{{ item.key }}</td>
                    <td class="text-wrap">{{ item.value }}</td>
                  </template>
                </tr>
              </tbody>
            </v-table>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <BackButton />
  </v-container>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, onBeforeUnmount, computed } from "vue";
import BackButton from "@/components/BackButton/index.vue";
import { useRouter } from "vue-router";
import { ipc } from "@/utils/ipcRenderer";

const router = useRouter();
// 状态变量
const connectionStatus = ref(false);
const connecting = ref(false);
const topic = ref("test_zjFMJxkJ/to_excavator");
const subscribed = ref(false);
const latestMessage = ref(null);
const messageDict = ref(null);
const mqttConfig = reactive({
  username: "",
  password: "",
  brokerUrl: "",
});
const mqttSubscription = reactive({
  mqttKeepLive: "",
  mqttToAndroid: "",
  mqttToExcavator: "",
});

// 解析消息内容为key-value对象
const parsedMessage = computed(() => {
  if (!latestMessage.value) return {};
  try {
    const content = JSON.parse(latestMessage.value.content);
    if (typeof content !== "object") return { value: content };

    // 根据messageDict转换key为中文
    const result = {};
    const currentDict = messageDict.value || {};
    const dict = [...currentDict.head, ...currentDict[`ID${content["id"]}`]];

    for (const key in content) {
      const chineseKey = dict.find((item) => item.key === key)?.label || key;
      result[chineseKey] = content[key];
    }

    return result;
  } catch (error) {
    return { value: latestMessage.value.content };
  }
});

// 将消息数据分组为每行两对key-value
const messageChunks = computed(() => {
  const entries = Object.entries(parsedMessage.value).map(([key, value]) => ({ key, value }));
  const chunks = [];
  for (let i = 0; i < entries.length; i += 2) {
    chunks.push(entries.slice(i, i + 2));
  }
  // 如果最后一组只有一个元素，补充一个空对象以保持布局
  if (entries.length % 2 !== 0) {
    chunks[chunks.length - 1].push({ key: "", value: "" });
  }
  return chunks;
});

// 连接/断开MQTT
async function toggleConnection() {
  connecting.value = true;
  try {
    if (!connectionStatus.value) {
      await ipc.invoke("controller/mqtt/connect", { ...mqttConfig });
      connectionStatus.value = true;
    } else {
      disconnect();
    }
  } catch (error) {
    console.error("MQTT connection error:", error);
  } finally {
    connecting.value = false;
  }
}

const disconnect = async () => {
  try {
    await ipc.invoke("controller/mqtt/disconnect");
    connectionStatus.value = false;
    subscribed.value = false;
    latestMessage.value = null;
  } catch (error) {
    console.error("MQTT disconnect error:", error);
  }
};

// 订阅/取消订阅主题
async function toggleSubscription() {
  try {
    if (!subscribed.value) {
      await ipc.invoke("controller/mqtt/subscribe", { topic: topic.value });
      subscribed.value = true;
    } else {
      await ipc.invoke("controller/mqtt/unsubscribe", { topic: topic.value });
      subscribed.value = false;
      latestMessage.value = null;
    }
  } catch (error) {
    console.error("MQTT subscription error:", error);
  }
}

// 处理接收到的MQTT消息
function handleMqttMessage(event, data) {
  latestMessage.value = {
    timestamp: new Date().toLocaleTimeString(),
    content: JSON.stringify(data),
  };
}

async function fetchMessageDict() {
  try {
    messageDict.value = await ipc.invoke("controller/message/getParserDict");
  } catch (error) {
    console.error("Failed to fetch message dictionary:", error);
  }

  let { mqttHost, mqttName, mqttPassword, mqttToAndroid, mqttToExcavator, mqttKeepLive } =
    await ipc.invoke("global-state:get", "android.mqttParams");
  mqttConfig.username = mqttName;
  mqttConfig.password = mqttPassword;
  mqttConfig.brokerUrl = mqttHost;
  mqttSubscription.mqttKeepLive = mqttKeepLive;
  mqttSubscription.mqttToAndroid = mqttToAndroid;
  mqttSubscription.mqttToExcavator = mqttToExcavator;
}

// 组件挂载时添加消息监听器
onMounted(async () => {
  ipc.on("mqtt-message", handleMqttMessage);
  await fetchMessageDict();
});

// 组件卸载时移除消息监听器
onUnmounted(() => {
  ipc.removeListener("mqtt-message", handleMqttMessage);
});

onBeforeUnmount(() => {
  disconnect();
});
</script>

<style scoped>
.text-wrap {
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
