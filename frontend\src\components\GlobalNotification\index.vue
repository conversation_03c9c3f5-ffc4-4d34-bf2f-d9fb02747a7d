<template>
  <div class="global-notification">
    <!-- 边缘闪烁提示 -->
    <div
      v-if="showEdgeAlert && displayMode === 'edge'"
      class="edge-alert"
      :class="[alertType, { flashing: isFlashing }]"
    ></div>

    <!-- 中央Snackbar提示 -->
    <v-snackbar
      v-model="showSnackbar"
      :color="snackbarColor"
      :timeout="displayDuration"
      :location="'bottom'"
      class="notification-snackbar"
      variant="elevated"
    >
      <div class="d-flex align-center">
        <v-icon :icon="snackbarIcon" class="mr-2" />
        <span>{{ message }}</span>
      </div>
      <template v-if="false" v-slot:actions>
        <v-btn variant="text" @click="showSnackbar = false"> 点击处理 </v-btn>
      </template>
    </v-snackbar>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from "vue";
import { ipc } from "@/utils/ipcRenderer";
import { registerWsHandler, unregisterWsHandler } from "@/utils/ipcMessageHandler";

// 通知状态
const showSnackbar = ref(false);
const showEdgeAlert = ref(false);
const isFlashing = ref(false);
const message = ref("");
const alertType = ref("info");
const displayMode = ref("snackbar"); // 'snackbar' 或 'edge'
const displayDuration = ref(3000); // 默认3秒
const displayCount = ref(1); // 默认显示1次
const displayFrequency = ref(0); // 默认频率，0表示立即显示
const currentDisplayCount = ref(0);

// 闪烁定时器
let flashingTimer = null;
let displayTimer = null;
let countTimer = null;

// 计算snackbar颜色
const snackbarColor = computed(() => {
  const colorMap = {
    success: "success",
    info: "info",
    warning: "warning",
    error: "error",
  };
  return colorMap[alertType.value] || "info";
});

// 计算snackbar图标
const snackbarIcon = computed(() => {
  const iconMap = {
    success: "mdi-check-circle",
    info: "mdi-information",
    warning: "mdi-alert",
    error: "mdi-alert-circle",
  };
  return iconMap[alertType.value] || "mdi-information";
});

// 显示通知的方法
const showNotification = (config) => {
  // 解构配置参数
  const { type = "info", content = "", mode = "snackbar", duration = 3000, count = 1, frequency = 0 } = config;

  // 设置通知参数
  message.value = content;
  alertType.value = type;
  displayMode.value = mode;
  displayDuration.value = duration;
  displayCount.value = count;
  displayFrequency.value = frequency;
  currentDisplayCount.value = 0;

  // 清除之前的定时器
  clearTimers();

  // 开始显示通知
  startDisplay();
};

// 开始显示通知
const startDisplay = () => {
  // 如果已经达到显示次数且不是无限显示(count=0)，则停止
  if (displayCount.value > 0 && currentDisplayCount.value >= displayCount.value) {
    return;
  }

  // 增加当前显示次数
  currentDisplayCount.value++;

  // 根据显示模式显示不同类型的通知
  if (displayMode.value === "edge") {
    showEdgeAlert.value = true;
    startFlashing();

    // 设置显示时长
    if (displayDuration.value > 0) {
      setTimeout(() => {
        stopFlashing();
        showEdgeAlert.value = false;

        // 如果需要继续显示，设置下一次显示的定时器
        scheduleNextDisplay();
      }, displayDuration.value);
    }
  } else {
    // 显示snackbar
    showSnackbar.value = true;

    // snackbar会自动根据timeout关闭，所以只需要设置下一次显示的定时器
    setTimeout(() => {
      scheduleNextDisplay();
    }, displayDuration.value);
  }
};

// 安排下一次显示
const scheduleNextDisplay = () => {
  // 如果已经达到显示次数且不是无限显示(count=0)，则停止
  if (displayCount.value > 0 && currentDisplayCount.value >= displayCount.value) {
    return;
  }

  // 如果频率大于0，则设置定时器
  if (displayFrequency.value > 0) {
    countTimer = setTimeout(() => {
      startDisplay();
    }, displayFrequency.value);
  } else if (displayFrequency.value === 0 && displayCount.value === 0) {
    // 如果频率为0且次数为0，则立即再次显示（无限循环）
    startDisplay();
  }
};

// 开始闪烁效果
const startFlashing = () => {
  isFlashing.value = true;
  // 闪烁效果通过CSS实现，不需要额外的JS逻辑
};

// 停止闪烁效果
const stopFlashing = () => {
  isFlashing.value = false;
};

// 清除所有定时器
const clearTimers = () => {
  if (flashingTimer) {
    clearInterval(flashingTimer);
    flashingTimer = null;
  }

  if (displayTimer) {
    clearTimeout(displayTimer);
    displayTimer = null;
  }

  if (countTimer) {
    clearTimeout(countTimer);
    countTimer = null;
  }
};

// 处理WebSocket消息
const handleWsMessage = (event, msg) => {
  showNotification({
    type: payload.type || "info",
    content: payload.content || "",
    mode: payload.mode || "snackbar",
    duration: payload.duration || 3000,
    count: payload.count !== undefined ? payload.count : 1,
    frequency: payload.frequency || 0,
  });
};

onMounted(() => {
  // 监听直接的IPC消息
  ipc.on("show-notification", (event, config) => {
    showNotification(config);
  });
});

// 组件卸载时清理
onUnmounted(() => {
  // 移除IPC监听器
  ipc.removeListener("show-notification", handleWsMessage);

  // 清除定时器
  clearTimers();
});

// 暴露方法给其他组件使用
defineExpose({
  showNotification,
});
</script>

<style scoped lang="scss">
.global-notification {
  position: fixed;
  z-index: 9999;
  pointer-events: none;
}

.edge-alert {
  position: fixed;
  z-index: 9999;

  &.success {
    border: 4px solid #4caf50;
    box-shadow: 0 0 15px rgba(76, 175, 80, 0.7);
  }

  &.info {
    border: 4px solid #2196f3;
    box-shadow: 0 0 15px rgba(33, 150, 243, 0.7);
  }

  &.warning {
    border: 4px solid #ff9800;
    box-shadow: 0 0 15px rgba(255, 152, 0, 0.7);
  }

  &.error {
    border: 4px solid #f44336;
    box-shadow: 0 0 15px rgba(244, 67, 54, 0.7);
  }

  &.flashing {
    animation: flash 0.8s infinite alternate;
  }

  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 4px;
  pointer-events: none;
}

@keyframes flash {
  from {
    opacity: 0.5;
  }
  to {
    opacity: 1;
  }
}

.notification-snackbar {
  font-size: 16px;

  :deep(.v-snackbar__wrapper) {
    min-width: 320px;
    max-width: 80%;
  }
}
</style>
