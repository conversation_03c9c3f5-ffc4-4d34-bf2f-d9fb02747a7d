<template>
  <v-container class="h-screen flex flex-col">
    <!-- 日志文件选择和筛选 -->
    <v-row class="flex-grow-0">
      <v-col cols="12" class="my-0 pt-1">
        <v-card>
          <v-card-text class="d-flex align-center gap-4">
            <v-select
              v-model="selectedLogFile"
              :items="logFiles"
              item-title="name"
              item-value="name"
              label="选择日志文件"
              hide-details
              class="flex-grow-1"
            >
              <template v-slot:item="{ props, item }">
                <v-list-item v-bind="props">
                  <v-list-item-title>{{ item.raw.name }}</v-list-item-title>
                  <v-list-item-subtitle>
                    修改时间: {{ new Date(item.raw.modifiedTime).toLocaleString() }}
                    大小: {{ (item.raw.size / 1024).toFixed(2) }}KB
                  </v-list-item-subtitle>
                </v-list-item>
              </template>
            </v-select>
            <v-text-field
              v-model="dateFilter.date"
              type="date"
              label="日期筛选"
              hide-details
              class="max-w-[200px]"
              @change="refreshLogFiles"
            ></v-text-field>
            <v-btn
              color="primary"
              @click="refreshLog"
              :loading="loading"
            >
              刷新
            </v-btn>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- 日志内容展示 -->
      <v-row class="flex-grow-1 overflow-hidden px-3">
        <v-col cols="12" class="h-full my-0 pt-1">
          <v-card class="h-full d-flex flex-column">
            <v-card-text class="flex-grow-1 overflow-y-auto log-scroll-container">
              <v-textarea
                v-model="logContent"
                readonly
                auto-grow
                rows="20"
                row-height="16"
                class="log-content"
                variant="outlined"
              ></v-textarea>
            </v-card-text>
            <v-card-actions>
              <v-pagination
                v-model="currentPage"
                :length="totalPages"
                :total-visible="7"
                @update:model-value="onPageChange"
              ></v-pagination>
            </v-card-actions>
          </v-card>
        </v-col>
      </v-row>
      
      <!-- 操作按钮组 -->
      <div class="fixed right-28 bottom-7 flex flex-col gap-3">
        <v-btn
          color="primary"
          icon="mdi-arrow-up"
          fab
          elevation="4"
          @click="scrollToTop"
          class="shadow-lg hover:scale-105 transition-transform"
        ></v-btn>
      </div>
    </v-row>

    <!-- 返回按钮 -->
    <BackButton />
  </v-container>
</template>

<script setup>
import { ref, watch, onMounted, onBeforeUnmount } from 'vue';
import { useRouter } from 'vue-router';
import { ipc } from '@/utils/ipcRenderer';
import BackButton from '@/components/BackButton/index.vue';

const router = useRouter();

// 状态变量
const loading = ref(false);
const logContent = ref('');
const logFiles = ref([]);
const selectedLogFile = ref('');
const currentPage = ref(1);
const totalPages = ref(1);
const dateFilter = ref({
  date: ''
});

// 取消日志监听的函数
let unwatchLog = null;

// 获取日志文件列表
async function refreshLogFiles() {
  try {
    const options = {};
    if (dateFilter.value.date) {
      options.date = new Date(dateFilter.value.date);
    }
    const files = await ipc.invoke('controller/log/getFiles', options);
    logFiles.value = files;
    if (files.length > 0 && !selectedLogFile.value) {
      selectedLogFile.value = files[0].name;
    }
  } catch (error) {
    console.error('获取日志文件列表失败:', error);
  }
}

// 刷新日志内容
async function refreshLog() {
  if (!selectedLogFile.value) return;
  
  loading.value = true;
  try {
    const result = await ipc.invoke('controller/log/readByPage', {
      logName: selectedLogFile.value,
      page: currentPage.value
    });
    logContent.value = result.content;
    totalPages.value = result.totalPages;
  } catch (error) {
    console.error('读取日志失败:', error);
  } finally {
    loading.value = false;
  }
}

// 监听日志变化
async function watchLog() {
  if (!selectedLogFile.value) return;

  try {
    // 先取消之前的监听
    if (unwatchLog) {
      await unwatchLog();
    }

    // 开始新的监听
    unwatchLog = await ipc.invoke('controller/log/watch', {
      logName: selectedLogFile.value,
      callback: () => {
        refreshLog(); // 当日志变化时重新获取当前页的内容
      }
    });
  } catch (error) {
    console.error('监听日志失败:', error);
  }
}

// 页码变化处理
function onPageChange() {
  refreshLog();
}

// 监听日志文件变化
watch(selectedLogFile, () => {
  currentPage.value = 1; // 切换文件时重置页码
  refreshLog();
  watchLog();
});

onMounted(() => {
  refreshLogFiles();
});

onBeforeUnmount(() => {
  if (unwatchLog) {
    unwatchLog();
  }
});

// 新增滚动到顶部方法
const scrollToTop = () => {
  document.querySelector('.log-scroll-container').scrollTo({
    top: 0,
    behavior: 'smooth'
  });
};
</script>

<style scoped>
.fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.log-scroll-container {
  max-height: calc(100vh - 186px);
  overflow-y: auto;
}

.h-screen {
  height: 100vh;
}

.flex-col {
  flex-direction: column;
}

.overflow-hidden {
  overflow: hidden;
}
.log-content {
  font-family: monospace;
  white-space: pre;
  font-size: 14px;
}
</style>