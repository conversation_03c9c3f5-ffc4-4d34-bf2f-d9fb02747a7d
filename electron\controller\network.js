"use strict";

const { logger } = require("ee-core/log");
const networkService = require("../service/networkService");

class NetworkController {
  /**
   * 所有方法接收两个参数
   * @param args 前端传的参数
   * @param event - ipc通信时才有值
   */

  /**
   * 开始网络监控
   * @param {Object} args 参数
   * @param {Number} args.interval 监控间隔，单位毫秒
   */
  async startMonitoring(args, event) {
    try {
      const { interval = 5000 } = args || {};
      // 开始监控
      const result = networkService.startMonitoring(interval);
      return result;
    } catch (error) {
      logger.error("[networkController] Failed to start monitoring:", error);
      throw error;
    }
  }

  /**
   * 停止网络监控
   */
  async stopMonitoring(args, event) {
    try {
      const result = networkService.stopMonitoring();
      return result;
    } catch (error) {
      logger.error("[networkController] Failed to stop monitoring:", error);
      throw error;
    }
  }

  /**
   * 获取当前监控状态
   */
  async getStatus(args, event) {
    try {
      return networkService.getStatus();
    } catch (error) {
      logger.error("[networkController] Failed to get status:", error);
      throw error;
    }
  }

  /**
   * 手动检测连通性
   */
  async checkConnectivity(args, event) {
    try {
      return await networkService.checkConnectivity();
    } catch (error) {
      logger.error("[networkController] Failed to check connectivity:", error);
      throw error;
    }
  }
}

module.exports = NetworkController;
