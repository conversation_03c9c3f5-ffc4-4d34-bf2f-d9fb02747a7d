import { ipc } from "./ipc<PERSON>enderer";

/**
 * 向Android发送消息的封装函数
 * @param {string} type - 消息类型，如 'sync' 或 'event' 或 'params'
 * @param {number} page - 页面编号
 * @param {object} payload - 消息负载数据
 * @returns {Promise<any>} - 返回ipc调用的Promise
 */
const send2Android = async (type, page, payload) => {
  return await ipc.invoke("controller/message/send2Android", {
    type,
    page,
    payload,
  });
};

/**
 * 发送同步类型消息到Android
 * @param {number} page - 页面编号
 * @param {object} payload - 消息负载数据
 * @returns {Promise<any>} - 返回ipc调用的Promise
 */
export const sendSync2Android = async (page, payload) => {
  return await send2Android("sync", page, payload);
};

/**
 * 发送事件类型消息到Android
 * @param {string} route - 事件路由
 * @param {object} [additionalData={}] - 附加数据
 * @returns {Promise<any>} - 返回ipc调用的Promise
 */
export const sendEvent2Android = async (route, additionalData = {}) => {
  return await send2Android("event", undefined, {
    route,
    ...additionalData,
  });
};

/**
 * 发送消息到Android
 * @param {string} route - 路径
 * @param {object} value - 值
 * @returns {Promise<any>} - 返回ipc调用的Promise
 */
export const sendParams2Android = async (route, value = {}, page = undefined) => {
  return await send2Android("params", page, {
    route,
    value,
  });
};

export const getAndroidAllParams = async () => {
  return await ipc.invoke("controller/message/send2Android", {
    type: "params",
    page: 300,
    payload: {
      route: "all",
    },
    communicationType: 2,
  });
};

export const getParamsFromAndroid = async (route, page = undefined) => {
  return await ipc.invoke("controller/message/send2Android", {
    type: "params",
    page,
    payload: {
      route,
    },
    communicationType: 2,
  });
};
