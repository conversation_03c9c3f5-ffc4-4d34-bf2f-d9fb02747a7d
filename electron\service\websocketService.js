"use strict";

const { logger } = require("ee-core/log");
const { getMainWindow } = require("ee-core/electron");
const WebSocket = require("ws");
const MessageParser = require("../utils/messageParser");
const MessageEncoder = require("../utils/messageEncoder");
const globalStateManager = require("./globalStateManager");
const { SenderStatus } = require("../utils/constants");

class WebSocketService {
  static instance = null;

  static getInstance() {
    if (!WebSocketService.instance) {
      WebSocketService.instance = new WebSocketService();
    }
    return WebSocketService.instance;
  }

  constructor() {
    if (WebSocketService.instance) {
      return WebSocketService.instance;
    }
    this.url = "";
    this.socket = null;
    this.reconnectTimer = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 10000; // 重连间隔3秒
    this.messageTimer1100 = null;
    this.messageInterval1100 = 20000; // 发送消息间隔1秒
    WebSocketService.instance = this;
  }

  // 连接到WebSocket服务器
  async connect() {
    if (this.socket) {
      this.disconnect();
    }

    try {
      const androidUrl = await globalStateManager.get("pad.androidUrl");
      const androidWSPort = await globalStateManager.get("pad.androidWSPort");
      this.url = `ws://${androidUrl}:${androidWSPort}`;
      this.socket = new WebSocket(this.url);

      // 连接成功事件
      this.socket.onopen = () => {
        logger.info("[websocketService] Connected to WebSocket server");
        this.reconnectAttempts = 0;
        if (this.reconnectTimer) {
          clearTimeout(this.reconnectTimer);
          this.reconnectTimer = null;
        }
        this.startSendMessage();
      };

      // 接收消息事件
      this.socket.onmessage = (event) => {
        this.handleMessage(event.data);
      };

      // 连接关闭事件
      this.socket.onclose = () => {
        logger.info("[websocketService] Connection closed");
        this.attemptReconnect();
      };

      // 错误事件
      this.socket.onerror = (error) => {
        logger.error("[websocketService] WebSocket error:", error);
      };
    } catch (error) {
      logger.error("[websocketService] Failed to connect:", error);
      this.attemptReconnect();
    }
  }

  // 断开连接
  disconnect() {
    if (this.messageTimer1100) {
      clearInterval(this.messageTimer1100);
      this.messageTimer1100 = null;
    }
    if (this.socket) {
      this.socket.close();
      this.socket = null;
      logger.info("[websocketService] Disconnected from WebSocket server");
    }
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  // 发送消息
  sendMessage(message) {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      try {
        this.socket.send(message);
      } catch (error) {
        logger.error("[websocketService] Failed to send message:", error);
      }
    } else {
      console.log("[websocketService] Cannot send message: connection not open");
    }
  }

  // 尝试重新连接
  attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts && !this.reconnectTimer) {
      this.reconnectTimer = setTimeout(() => {
        logger.info(
          `[websocketService] Attempting to reconnect (${this.reconnectAttempts + 1}/${this.maxReconnectAttempts})`
        );
        this.reconnectAttempts++;
        this.connect();
      }, this.reconnectInterval);
    }
  }

  // 检查连接状态
  isConnected() {
    return this.socket && this.socket.readyState === WebSocket.OPEN;
  }

  // 处理接收到的消息
  async handleMessage(data) {
    try {
      const msg = MessageParser.parse(data, "mqtt");

      switch (msg.id) {
        case 2048:
          // console.log("msg2048", msg);
          break;
        case 20:
          this.handleMessageID20(msg);
          break;
        case 11:
          this.handleMessageID11(msg);
          break;
        default:
          break;
      }
    } catch (error) {
      logger.error("[websocketService] Failed to parse message:", error);
    }
  }

  startSendMessage() {
    if (this.messageTimer1100) {
      clearInterval(this.messageTimer1100);
    }

    this.messageTimer1100 = setInterval(async () => {
      const body = {
        vehicleSubType: 0,
        tabletStatus: 64, // 64远控
        failureLevel: 255, // 255没有失效
      };
      const msgBody = await MessageEncoder.buildBody(1100, body);

      const head = {
        vehicleType: 0,
        dataUnitLength: msgBody.length,
        transmitCount: MessageEncoder.getTransmitCount(1100),
      };

      const msgHead = MessageEncoder.buildHead(head);
      const mergedBuffer = Buffer.concat([msgHead, msgBody]);
      // console.log(mergedBuffer);
      this.sendMessage(mergedBuffer);
    }, this.messageInterval1100);

    setTimeout(() => {
      this.getAllDataFromAndroid();
    }, 5000);
  }

  async getAllDataFromAndroid() {
    const body = {
      communicationType: 2,
      jsonData: {
        page: 300,
        type: "params",
        payload: {
          route: "all",
        },
      },
    };
    await this.sendMessageID20(body);
  }

  async sendMessageID20(body) {
    const msgBody = await MessageEncoder.buildBody(20, body);
    const head = {
      vehicleType: 0,
      dataUnitLength: msgBody.length,
      transmitCount: MessageEncoder.getTransmitCount(20),
    };

    const msgHead = MessageEncoder.buildHead(head);
    const mergedBuffer = Buffer.concat([msgHead, msgBody]);
    this.sendMessage(mergedBuffer);

    const msg = MessageParser.parse(mergedBuffer, "mqtt");
    console.log("sendMessageID20", msg);
  }

  /**
   * 处理ID为20的消息
   * @param {Object} msg - 解析后的消息对象
   */
  async handleMessageID20(msg) {
    console.log("handleMessageID20", msg);
    try {
      const mainWindow = getMainWindow();
      if (mainWindow) {
        mainWindow.webContents.send("ipc-message", msg);
      }
      // page id 为300，则更新整个GlobalStateManager
      if (msg.jsonData && msg.jsonData.page === 300 && msg.jsonData.payload) {
        try {
          // 使用新添加的setAll方法更新全局状态
          const result = await globalStateManager.setAll(msg.jsonData.payload);
          if (result) {
            console.log("[websocketService] Global state updated successfully" + JSON.stringify(msg.jsonData.payload));
            mainWindow.webContents.send("show-notification", { type: "success", content: "同步操作台数据成功" });
          }

          let localAndroid = await globalStateManager.get("localAndroid");
          console.log("localAndroid", localAndroid);
        } catch (error) {
          logger.error("[websocketService] Error updating global state:", error);
        }
      }

      console.info("[websocketService] Handled message with ID 20" + JSON.stringify(msg));
    } catch (error) {
      console.error("[websocketService] Error handling message ID 20:", error);
    }
  }

  /**
   * 处理ID为11的消息
   * @param {Object} msg - 解析后的消息对象
   */
  async handleMessageID11(msg) {
    try {
      if (msg["senderStatus"]) {
        let status = 8;
        switch (msg["senderStatus"]) {
          case SenderStatus.INITIALIZE:
            status = SenderStatus.INITIALIZE;
            break;
          case SenderStatus.WAITING:
          case SenderStatus.DEBUGGING:
          case SenderStatus.ASYNC_MONITORING:
            status = SenderStatus.WAITING;
            break;
          case SenderStatus.LOGIN:
          case SenderStatus.REMOTE_CONTROL:
          case SenderStatus.LOCKED:
          case SenderStatus.FAILSAFE:
          case SenderStatus.LOOPBACK_TEST:
            status = SenderStatus.REMOTE_CONTROL;
            break;
        }
        let curStu = await globalStateManager.get("pad.status");
        if (curStu !== status) {
          await globalStateManager.set("pad.status", status);
          const mainWindow = getMainWindow();
          if (mainWindow) {
            msg.padStatus = status;
            mainWindow.webContents.send("ipc-message", msg);
          }
        }
      }

      await this.handleSendHeartbeat(msg);
      // console.log("[websocketService] Handled message with ID 11");
    } catch (error) {
      console.log("[websocketService] Error handling message ID 11:", error);
    }
  }

  async handleSendHeartbeat(msg) {
    let padStu = await globalStateManager.get("pad.status");

    const body = {
      senderStatus: padStu,
      confirmedTransmitCount: msg.transmitCount,
    };
    const msgBody = await MessageEncoder.buildBody(12, body);

    const head = {
      vehicleType: 0,
      dataUnitLength: msgBody.length,
      transmitCount: MessageEncoder.getTransmitCount(11),
    };
    const msgHead = MessageEncoder.buildHead(head);

    const mergedBuffer = Buffer.concat([msgHead, msgBody]);

    this.sendMessage(mergedBuffer);
  }
}

module.exports = WebSocketService.getInstance();
