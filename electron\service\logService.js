'use strict';

const fs = require('fs');
const path = require('path');
const { app } = require('electron');
const { getLogDir } = require('ee-core/ps');
const { logger } = require('ee-core/log');

class LogService {
  constructor() {
    // this.logDir = path.join(app.getPath('userData'), 'logs');
    this.logDir = getLogDir();
    this.pageSize = 1000; // 默认每页显示1000行
    this.cleanupInterval = null; // 清理日志的定时器
  }

  /**
   * 获取日志文件路径
   * @param {string} logName - 日志文件名
   * @returns {string} 日志文件完整路径
   */
  getLogPath(logName) {
    return path.join(this.logDir, logName);
  }

  /**
   * 读取日志文件内容
   * @param {string} logName - 日志文件名
   * @returns {Promise<string>} 日志内容
   */
  async readLog(logName) {
    try {
      const logPath = this.getLogPath(logName);
      if (!fs.existsSync(logPath)) {
        return '';
      }
      const content = await fs.promises.readFile(logPath, 'utf8');
      return content;
    } catch (error) {
      logger.error('读取日志文件失败:', error);
      return '';
    }
  }

  /**
   * 监听日志文件变化
   * @param {string} logName - 日志文件名
   * @param {Function} callback - 回调函数，参数为新的日志内容
   * @returns {Function} 取消监听的函数
   */
  watchLog(logName, callback) {
    const logPath = this.getLogPath(logName);
    const watcher = fs.watch(logPath, async (eventType) => {
      if (eventType === 'change') {
        const content = await this.readLog(logName);
        callback(content);
      }
    });

    return () => watcher.close();
  }

  /**
   * 获取日志文件列表
   * @param {Object} options - 过滤选项
   * @param {Date} [options.date] - 指定日期
   * @returns {Promise<Array<Object>>} 日志文件列表，包含文件名和最后修改时间
   */
  async getLogFiles(options = {}) {
    try {
      const files = await fs.promises.readdir(this.logDir);
      let logFiles = await Promise.all(
        files.map(async (file) => {
          const filePath = path.join(this.logDir, file);
          const stats = await fs.promises.stat(filePath);
          return {
            name: file,
            modifiedTime: stats.mtime,
            size: stats.size
          };
        })
      );

      // 按日期过滤
      if (options.date) {
        const startDate = new Date(options.date);
        startDate.setHours(0, 0, 0, 0);
        const endDate = new Date(options.date);
        endDate.setHours(23, 59, 59, 999);

        logFiles = logFiles.filter(file => {
          const fileTime = new Date(file.modifiedTime);
          return fileTime >= startDate && fileTime <= endDate;
        });
      }

      // 按修改时间降序排序
      return logFiles.sort((a, b) => b.modifiedTime - a.modifiedTime);
    } catch (error) {
      logger.error('获取日志文件列表失败:', error);
      return [];
    }
  }

  /**
   * 分页读取日志文件内容
   * @param {string} logName - 日志文件名
   * @param {number} page - 页码，从1开始
   * @param {number} [pageSize] - 每页行数，默认使用构造函数中设置的值
   * @returns {Promise<Object>} 包含日志内容和分页信息的对象
   */
  async readLogByPage(logName, page = 1, pageSize = this.pageSize) {
    try {
      const logPath = this.getLogPath(logName);
      if (!fs.existsSync(logPath)) {
        return {
          content: '',
          totalLines: 0,
          currentPage: page,
          totalPages: 0
        };
      }

      const content = await fs.promises.readFile(logPath, 'utf8');
      const lines = content.split('\n');
      const totalLines = lines.length;
      const totalPages = Math.ceil(totalLines / pageSize);
      const startIndex = (page - 1) * pageSize;
      const endIndex = Math.min(startIndex + pageSize, totalLines);
      const pageContent = lines.slice(startIndex, endIndex).join('\n');

      return {
        content: pageContent,
        totalLines,
        currentPage: page,
        totalPages
      };
    } catch (error) {
      logger.error('分页读取日志文件失败:', error);
      return {
        content: '',
        totalLines: 0,
        currentPage: page,
        totalPages: 0
      };
    }
  }
  /**
   * 清理指定天数之前的日志文件
   * @param {number} days - 天数，默认为30天
   * @returns {Promise<Object>} 清理结果，包含删除的文件数量和文件列表
   */
  async cleanupOldLogs(days = 30) {
    try {
      const files = await fs.promises.readdir(this.logDir);
      const now = new Date();
      const cutoffDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);

      const deletedFiles = [];
      let deletedCount = 0;

      for (const file of files) {
        const filePath = path.join(this.logDir, file);
        const stats = await fs.promises.stat(filePath);
        const fileDate = new Date(stats.mtime);

        if (fileDate < cutoffDate) {
          await fs.promises.unlink(filePath);
          deletedFiles.push({
            name: file,
            modifiedTime: stats.mtime,
            size: stats.size
          });
          deletedCount++;
        }
      }

      logger.info(`已清理 ${deletedCount} 个超过 ${days} 天的日志文件`);
      return {
        count: deletedCount,
        files: deletedFiles
      };
    } catch (error) {
      logger.error('清理日志文件失败:', error);
      return {
        count: 0,
        files: [],
        error: error.message
      };
    }
  }

  /**
   * 启动定时清理日志任务
   * @param {number} days - 清理多少天之前的日志，默认30天
   * @param {number} interval - 清理间隔，默认24小时（单位：毫秒）
   */
  startAutoCleanup(days = 30, interval = 24 * 60 * 60 * 1000) {
    // 先停止之前的定时器
    this.stopAutoCleanup();

    // 立即执行一次清理
    this.cleanupOldLogs(days);

    // 设置定时清理
    this.cleanupInterval = setInterval(() => {
      this.cleanupOldLogs(days);
    }, interval);

    logger.info(`已启动定时清理日志任务，将清理 ${days} 天前的日志，间隔 ${interval / (60 * 60 * 1000)} 小时`);
  }

  /**
   * 停止定时清理日志任务
   */
  stopAutoCleanup() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
      logger.info('已停止定时清理日志任务');
    }
  }
}

module.exports = LogService;