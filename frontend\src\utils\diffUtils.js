/**
 * 比较两个对象的差异，返回差异路径和新值
 * @param {Object} newObj - 新对象
 * @param {Object} oldObj - 旧对象
 * @returns {Array} - 差异数组，每个元素包含路径和新值
 */
export function findDifferences(newObj, oldObj) {
  const differences = [];
  
  /**
   * 递归比较对象差异
   * @param {Object} newVal - 新值
   * @param {Object} oldVal - 旧值
   * @param {String} path - 当前路径
   */
  function compareObjects(newVal, oldVal, path = '') {
    // 如果两个值类型不同，直接记录差异
    if (typeof newVal !== typeof oldVal) {
      differences.push({ path, newValue: newVal });
      return;
    }
    
    // 如果不是对象或数组，直接比较值
    if (typeof newVal !== 'object' || newVal === null) {
      if (newVal !== oldVal) {
        differences.push({ path, newValue: newVal });
      }
      return;
    }
    
    // 处理数组
    if (Array.isArray(newVal)) {
      if (!Array.isArray(oldVal) || newVal.length !== oldVal.length) {
        differences.push({ path, newValue: newVal });
        return;
      }
      
      // 比较数组中的每个元素
      for (let i = 0; i < newVal.length; i++) {
        compareObjects(newVal[i], oldVal[i], path ? `${path}.${i}` : `${i}`);
      }
      return;
    }
    
    // 处理对象
    const newKeys = Object.keys(newVal);
    const oldKeys = Object.keys(oldVal);
    
    // 检查新增或修改的属性
    for (const key of newKeys) {
      const newPath = path ? `${path}.${key}` : key;
      if (!(key in oldVal)) {
        differences.push({ path: newPath, newValue: newVal[key] });
      } else {
        compareObjects(newVal[key], oldVal[key], newPath);
      }
    }
    
    // 检查删除的属性
    for (const key of oldKeys) {
      if (!(key in newVal)) {
        const newPath = path ? `${path}.${key}` : key;
        differences.push({ path: newPath, newValue: undefined });
      }
    }
  }
  
  compareObjects(newObj, oldObj);
  return differences;
}