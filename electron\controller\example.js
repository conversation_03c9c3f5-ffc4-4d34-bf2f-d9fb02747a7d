"use strict";

const { logger } = require("ee-core/log");
const { app } = require("electron");
const MessageEncoder = require("../utils/messageEncoder");
const MessageParser = require("../utils/messageParser");
const streamService = require('../service/streamService');
const WebSocketService = require('../service/websocketService');


/**
 * example
 * @class
 */
class ExampleController {
  /**
   * 所有方法接收两个参数
   * @param args 前端传的参数
   * @param event - ipc通信时才有值。详情见：控制器文档
   */

  async quit() {
    logger.info("退出系统:", { status: "ok" });
    app.quit();
    return "退出系统";
  }
  async buildHead(args, event) {
    let res = MessageEncoder.buildHead(args);
    console.log(res);
    let pm = MessageParser.parse(res, "mqtt");
    console.log(pm);
    return res;
  }
  async startStream() {
    streamService.startStream();
    logger.info("开始流:", { status: "ok" });
  }
  async stopStream() {
    streamService.stopStream();
    logger.info("停止流:", { status: "ok" });
  }
  async getID20() {
    await WebSocketService.sendMessageID20();
    console.log("发送ID20:", { status: "ok" });
  }
}
ExampleController.toString = () => "[class ExampleController]";

module.exports = ExampleController;
