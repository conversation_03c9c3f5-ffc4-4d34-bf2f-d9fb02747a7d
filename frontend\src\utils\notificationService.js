import { ipc } from "./ipcRenderer";

/**
 * 通知服务
 * 提供全局通知功能
 */
class NotificationService {
  /**
   * 显示通知
   * @param {Object} config - 通知配置
   * @param {string} config.type - 通知类型: 'success', 'info', 'warning', 'error'
   * @param {string} config.content - 通知内容
   * @param {string} config.mode - 显示模式: 'snackbar', 'edge'
   * @param {number} config.duration - 显示时长(毫秒)
   * @param {number} config.count - 显示次数，0表示无限
   * @param {number} config.frequency - 显示频率(毫秒)，0表示立即显示
   */
  showNotification(config) {
    ipc.invoke("controller/message/sendNotification", config);
  }

  /**
   * 显示成功通知
   * @param {string} content - 通知内容
   * @param {Object} options - 其他配置选项
   */
  success(content, options = {}) {
    this.showNotification({
      type: "success",
      content,
      ...options,
    });
  }

  /**
   * 显示信息通知
   * @param {string} content - 通知内容
   * @param {Object} options - 其他配置选项
   */
  info(content, options = {}) {
    this.showNotification({
      type: "info",
      content,
      ...options,
    });
  }

  /**
   * 显示警告通知
   * @param {string} content - 通知内容
   * @param {Object} options - 其他配置选项
   */
  warning(content, options = {}) {
    this.showNotification({
      type: "warning",
      content,
      ...options,
    });
  }

  /**
   * 显示错误通知
   * @param {string} content - 通知内容
   * @param {Object} options - 其他配置选项
   */
  error(content, options = {}) {
    this.showNotification({
      type: "error",
      content,
      ...options,
    });
  }
}

// 创建单例实例
const notificationService = new NotificationService();

export default notificationService;
