/**
 * 智能功能设置适配器
 * 用于转换服务端不规范的变量结构为前端友好的格式
 * 同时在与服务端通信时保持原始格式不变
 */

/**
 * 将服务端格式转换为前端友好的格式
 * @param {Object} serverSettings - 服务端格式的设置
 * @returns {Object} - 前端友好的格式设置
 */
export function toFrontendFormat(serverSettings) {
  if (!serverSettings) return {};
  // 严格按照最新前端参数结构转换
  return {
    pedestrian: {
      enabled: !!serverSettings.have_person_flag,
      switch: !!serverSettings.has_person_flag?.switch
    },
    posture: {
      enabled: !!serverSettings.haveAttitudePerception,
      switch: !!serverSettings.attitude_perception?.switch,
      alarmSound: !!serverSettings.attitude_perception?.switch2,
      tiltScheme: Array.isArray(serverSettings.tiltAlarmPlatform) ? serverSettings.tiltAlarmPlatform.map(item => ({
        isUse: !!item.isUse,
        maxXValue: Number(item.maxXValue),
        maxYValue: Number(item.maxYValue),
        minXValue: Number(item.minXValue),
        minYValue: Number(item.minYValue),
        name: item.name,
        warmXValue: Number(item.warmXValue),
        warmYValue: Number(item.warmYValue)
      })) : [
        {
          isUse: true,
          maxXValue: 45,
          maxYValue: 45,
          minXValue: 5,
          minYValue: 5,
          name: "A",
          warmXValue: 40,
          warmYValue: 26
        },
        {
          isUse: false,
          maxXValue: 45,
          maxYValue: 45,
          minXValue: 5,
          minYValue: 5,
          name: "B",
          warmXValue: 14,
          warmYValue: 14
        }
      ]
    },
    terrain: {
      enabled: !!serverSettings.have_ground_level,
      switch: !!serverSettings.ground_level?.switch,
      color: serverSettings.ground_level?.color ? "#" + serverSettings.ground_level.color.replace(/^#/,"") : "#00FF00",
      opacity: typeof serverSettings.ground_level?.transparency === "number" ? serverSettings.ground_level.transparency : 50
    },
    sideRadar: {
      enabled: !!serverSettings.have_side_profile_radar,
      switch: !!serverSettings.side_profile_radar?.switch,
      color: serverSettings.side_profile_radar?.color ? "#" + serverSettings.side_profile_radar.color.replace(/^#/,"") : "#00FF00",
      opacity: typeof serverSettings.side_profile_radar?.transparency === "number" ? serverSettings.side_profile_radar.transparency : 50
    },
    collisionPrevention: {
      enabled: !!serverSettings.have_robot_anti_collision,
      switch: !!serverSettings.aif_robot_anti_collision?.switch,
      distanceAlarm: !!serverSettings.aif_robot_anti_collision?.switch2,
      autoStop: !!serverSettings.aif_robot_anti_collision?.switch3,
      radarGreenValue: serverSettings.radarValues?.radarGreenValue ?? 8.8,
      radarRedValue: serverSettings.radarValues?.radarRedValue ?? 1.6,
      radarYellowValue: serverSettings.radarValues?.radarYellowValue ?? 4.1
    },
    bucketLanding: {
      enabled: !!serverSettings.have_bucket_landing_point,
      switch: !!serverSettings.bucket_landing_point?.switch,
      type: typeof serverSettings.bucket_landing_point?.form === "number" ? serverSettings.bucket_landing_point.form : 0,
      color: serverSettings.bucket_landing_point?.color ? "#" + serverSettings.bucket_landing_point.color.replace(/^#/,"") : "#00FF00",
      opacity: typeof serverSettings.bucket_landing_point?.transparency === "number" ? serverSettings.bucket_landing_point.transparency : 50
    },
    bucketTooth: {
      enabled: !!serverSettings.have_bucket_tooth,
      switch: !!serverSettings.bucket_tooth?.switch
    },
    dust: {
      enabled: !!serverSettings.have_dust_seen_through,
      switch: !!serverSettings.aif_dust_seen_through?.switch,
      opacity: typeof serverSettings.aif_dust_seen_through?.transparency === "number" ? serverSettings.aif_dust_seen_through.transparency : 50
    },
    remotePower: {
      enabled: !!serverSettings.have_remote_power_on,
      powerOn: !!serverSettings.remote_power_on?.switch,
      powerOff: !!serverSettings.remote_power_on?.switch2
    },
    autoReset: {
      enabled: !!serverSettings.have_auto_reset,
      switch: !!serverSettings.auto_reset?.switch
    }
  };
}

/**
 * 将前端友好的格式转换回服务端格式
 * @param {Object} frontendSettings - 前端友好的格式设置
 * @param {Object} originalServerSettings - 原始服务端格式设置（用于保留未修改的字段）
 * @returns {Object} - 服务端格式的设置
 */
export function toServerFormat(frontendSettings, originalServerSettings = {}) {
  if (!frontendSettings) return originalServerSettings;
  const result = { ...originalServerSettings };

  // 行人识别
  if (frontendSettings.pedestrian) {
    result.have_person_flag = !!frontendSettings.pedestrian.enabled;
    result.has_person_flag = {
      ...originalServerSettings.has_person_flag,
      switch: frontendSettings.pedestrian.switch ? 1 : 0,
      // 保留原始服务端的 color, transparency, form 或使用默认值
      color: originalServerSettings.has_person_flag?.color ?? "FF0000", // 默认红色
      transparency: originalServerSettings.has_person_flag?.transparency ?? 0,
      form: originalServerSettings.has_person_flag?.form ?? 0
    };
  }

  // 姿态检测
  if (frontendSettings.posture) {
    result.haveAttitudePerception = !!frontendSettings.posture.enabled;
    result.attitude_perception = {
      ...originalServerSettings.attitude_perception,
      switch: frontendSettings.posture.switch ? 1 : 0,
      switch2: frontendSettings.posture.alarmSound ? 1 : 0,
      // 保留原始服务端的 color, transparency, form 或使用默认值
      color: originalServerSettings.attitude_perception?.color ?? "00FF00", // 默认绿色
      transparency: originalServerSettings.attitude_perception?.transparency ?? 100,
      form: originalServerSettings.attitude_perception?.form ?? 0
    };
    // 倾斜报警平台配置 - 仅当 frontendSettings 中存在时更新
    if (Array.isArray(frontendSettings.posture.tiltScheme)) {
      result.tiltAlarmPlatform = frontendSettings.posture.tiltScheme.map(item => ({
        isUse: !!item.isUse,
        maxXValue: Number(item.maxXValue),
        maxYValue: Number(item.maxYValue),
        minXValue: Number(item.minXValue),
        minYValue: Number(item.minYValue),
        name: item.name,
        warmXValue: Number(item.warmXValue),
        warmYValue: Number(item.warmYValue)
      }));
    } else if (originalServerSettings.tiltAlarmPlatform) {
      // 如果前端没有提供，则保留原始服务端的配置
      result.tiltAlarmPlatform = originalServerSettings.tiltAlarmPlatform;
    }
  }

  // 地面平整度
  if (frontendSettings.terrain) {
    result.have_ground_level = !!frontendSettings.terrain.enabled;
    result.ground_level = {
      ...originalServerSettings.ground_level,
      switch: frontendSettings.terrain.switch ? 1 : 0,
      color: (frontendSettings.terrain.color || originalServerSettings.ground_level?.color || "#00FF00").replace(/^#/, ""),
      transparency: typeof frontendSettings.terrain.opacity === "number" ? frontendSettings.terrain.opacity : (originalServerSettings.ground_level?.transparency ?? 50),
      form: originalServerSettings.ground_level?.form ?? 0 // 保留原始 form 或默认值
    };
  }

  // 侧剖面雷达
  if (frontendSettings.sideRadar) {
    result.have_side_profile_radar = !!frontendSettings.sideRadar.enabled;
    result.side_profile_radar = {
      ...originalServerSettings.side_profile_radar,
      switch: frontendSettings.sideRadar.switch ? 1 : 0,
      color: (frontendSettings.sideRadar.color || originalServerSettings.side_profile_radar?.color || "#00FF00").replace(/^#/, ""),
      transparency: typeof frontendSettings.sideRadar.opacity === "number" ? frontendSettings.sideRadar.opacity : (originalServerSettings.side_profile_radar?.transparency ?? 50),
      form: originalServerSettings.side_profile_radar?.form ?? 0 // 保留原始 form 或默认值
    };
  }

  // 防碰撞预警
  if (frontendSettings.collisionPrevention) {
    result.have_robot_anti_collision = !!frontendSettings.collisionPrevention.enabled;
    result.aif_robot_anti_collision = {
      ...originalServerSettings.aif_robot_anti_collision,
      switch: frontendSettings.collisionPrevention.switch ? 1 : 0,
      switch2: frontendSettings.collisionPrevention.distanceAlarm ? 1 : 0,
      switch3: frontendSettings.collisionPrevention.autoStop ? 1 : 0,
      // 保留原始服务端的 color, transparency, form 或使用默认值
      color: originalServerSettings.aif_robot_anti_collision?.color ?? "00FFFE", // 默认青色
      transparency: originalServerSettings.aif_robot_anti_collision?.transparency ?? 100,
      form: originalServerSettings.aif_robot_anti_collision?.form ?? 1
    };
    // 雷达相关参数 - 优先使用前端值，其次原始值，最后默认值
    result.radarValues = {
      ...originalServerSettings.radarValues,
      radarGreenValue: frontendSettings.collisionPrevention.radarGreenValue ?? originalServerSettings.radarValues?.radarGreenValue ?? 8.8,
      radarRedValue: frontendSettings.collisionPrevention.radarRedValue ?? originalServerSettings.radarValues?.radarRedValue ?? 1.6,
      radarYellowValue: frontendSettings.collisionPrevention.radarYellowValue ?? originalServerSettings.radarValues?.radarYellowValue ?? 4.1
    };
  }

  // 铲斗落点
  if (frontendSettings.bucketLanding) {
    result.have_bucket_landing_point = !!frontendSettings.bucketLanding.enabled;
    result.bucket_landing_point = {
      ...originalServerSettings.bucket_landing_point,
      switch: frontendSettings.bucketLanding.switch ? 1 : 0,
      color: (frontendSettings.bucketLanding.color || originalServerSettings.bucket_landing_point?.color || "#00FF00").replace(/^#/, ""),
      transparency: typeof frontendSettings.bucketLanding.opacity === "number" ? frontendSettings.bucketLanding.opacity : (originalServerSettings.bucket_landing_point?.transparency ?? 50),
      form: typeof frontendSettings.bucketLanding.type === "number" ? frontendSettings.bucketLanding.type : (originalServerSettings.bucket_landing_point?.form ?? 0)
    };
  }

  // 斗齿识别
  if (frontendSettings.bucketTooth) {
    result.have_bucket_tooth = !!frontendSettings.bucketTooth.enabled;
    result.bucket_tooth = {
      ...originalServerSettings.bucket_tooth,
      switch: frontendSettings.bucketTooth.switch ? 1 : 0,
      // 保留原始服务端的 color, transparency, form 或使用默认值
      color: originalServerSettings.bucket_tooth?.color ?? "FEFE3E", // 默认黄色
      transparency: originalServerSettings.bucket_tooth?.transparency ?? 0,
      form: originalServerSettings.bucket_tooth?.form ?? 0
    };
  }

  // 粉尘透视
  if (frontendSettings.dust) {
    result.have_dust_seen_through = !!frontendSettings.dust.enabled;
    result.aif_dust_seen_through = {
      ...originalServerSettings.aif_dust_seen_through,
      switch: frontendSettings.dust.switch ? 1 : 0,
      // 保留原始服务端的 color, form 或使用默认值
      color: originalServerSettings.aif_dust_seen_through?.color ?? "00FFFE", // 默认青色
      transparency: typeof frontendSettings.dust.opacity === "number" ? frontendSettings.dust.opacity : (originalServerSettings.aif_dust_seen_through?.transparency ?? 50),
      form: originalServerSettings.aif_dust_seen_through?.form ?? 1
    };
  }

  // 远程上电
  if (frontendSettings.remotePower) {
    result.have_remote_power_on = !!frontendSettings.remotePower.enabled;
    result.remote_power_on = {
      ...originalServerSettings.remote_power_on,
      switch: frontendSettings.remotePower.powerOn ? 1 : 0,
      switch2: frontendSettings.remotePower.powerOff ? 1 : 0
    };
  }

  // 自动复位
  if (frontendSettings.autoReset) {
    result.have_auto_reset = !!frontendSettings.autoReset.enabled;
    result.auto_reset = {
      ...originalServerSettings.auto_reset,
      switch: frontendSettings.autoReset.switch ? 1 : 0,
      // 保留原始服务端的 color, transparency, form 或使用默认值
      color: originalServerSettings.auto_reset?.color ?? "00FF3A", // 默认绿色
      transparency: originalServerSettings.auto_reset?.transparency ?? 100,
      form: originalServerSettings.auto_reset?.form ?? 0
    };
  }

  return result;
}

/**
 * 使用示例：
 * 
 * // 从服务端获取设置后转换为前端友好格式
 * import { toFrontendFormat, toServerFormat } from '@/utils/intelligenceAdapter';
 * 
 * // 获取服务端数据
 * const serverSettings = await api.getSettings();
 * 
 * // 转换为前端友好格式
 * const frontendSettings = toFrontendFormat(serverSettings);
 * 
 * // 在组件中使用前端友好格式
 * const settings = ref(frontendSettings);
 * 
 * // 保存设置时，转换回服务端格式
 * const saveSettings = async () => {
 *   const updatedServerSettings = toServerFormat(settings.value, serverSettings);
 *   await api.saveSettings(updatedServerSettings);
 * };
 */